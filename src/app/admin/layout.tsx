'use client'

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion } from "framer-motion";
import { Card, CardBody, Button } from "@heroui/react";
import { useAuth } from "@/contexts/AuthContext";
import AdminProtectedRoute from "@/components/AdminProtectedRoute";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { user, signOut } = useAuth();

  // Don't protect login and unauthorized pages
  const isAuthPage = pathname === '/admin/login' || pathname === '/admin/unauthorized';

  const handleLogout = async () => {
    await signOut();
  };

  const menuItems = [
    { href: '/admin', label: 'Dashboard', icon: '■' },
    { href: '/admin/blogs', label: 'จัดการบทความ', icon: '▣' },
    { href: '/admin/blogs/new', label: 'เขียนบทความใหม่', icon: '✚' },
    { href: '/admin/settings', label: 'ตั้งค่า', icon: '⚙' },
  ];

  // If it's an auth page, render without protection
  if (isAuthPage) {
    return children;
  }

  return (
    <AdminProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-black via-blue-900/20 to-black overflow-hidden">
        {/* Animated Background Elements */}
        <div className="fixed inset-0 overflow-hidden -z-10">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/30 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse delay-2000"></div>

          {/* Floating Elements */}
          <div className="absolute top-20 right-20 opacity-10">
            <div className="w-32 h-20 border border-purple-500/30 rounded bg-black/20 backdrop-blur-sm"></div>
          </div>
          <div className="absolute bottom-32 left-20 opacity-10">
            <div className="w-24 h-16 border border-blue-500/30 rounded bg-black/20 backdrop-blur-sm"></div>
          </div>
        </div>

        <div className="relative z-10 flex">
          {/* Sidebar */}
          <motion.div
            initial={{ x: -250 }}
            animate={{ x: sidebarOpen ? 0 : -200 }}
            transition={{ duration: 0.3 }}
            className={`fixed left-0 top-0 h-full bg-black/40 backdrop-blur-md border-r border-blue-500/20 z-50 shadow-2xl shadow-blue-500/10 ${sidebarOpen ? 'w-72' : 'w-16'
              }`}
          >
            <div className="p-4">
              {/* Logo */}
              <div className="flex items-center gap-3 mb-8">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                  <span className="text-white font-bold text-xl">T</span>
                </div>
                {sidebarOpen && (
                  <div>
                    <h1 className="text-white font-bold text-xl bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">TraderBase</h1>
                    <p className="text-gray-300 text-sm">ระบบจัดการเว็บไซต์</p>
                  </div>
                )}
              </div>

              {/* Toggle Button */}
              <Button
                onPress={() => setSidebarOpen(!sidebarOpen)}
                variant="ghost"
                className="mb-8 text-white hover:bg-blue-500/20 border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300"
                size="sm"
              >
                {sidebarOpen ? '◀' : '▶'}
              </Button>

              {/* Menu Items */}
              <nav className="space-y-2">
                {menuItems.map((item) => (
                  <Link key={item.href} href={item.href}>
                    <div
                      className={`flex items-center gap-4 p-4 rounded-xl transition-all duration-300 ${pathname === item.href
                        ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-300 border border-blue-500/30 shadow-lg shadow-blue-500/10'
                        : 'text-gray-300 hover:bg-white/10 hover:text-white hover:border-white/20 border border-transparent'
                        }`}
                    >
                      <span className="text-2xl">{item.icon}</span>
                      {sidebarOpen && (
                        <span className="font-semibold text-lg">{item.label}</span>
                      )}
                    </div>
                  </Link>
                ))}
              </nav>
            </div>

            {/* User Info */}
            {sidebarOpen && user && (
              <div className="absolute bottom-6 left-4 right-4">
                <Card className="bg-black/40 backdrop-blur-sm border border-blue-500/20 shadow-lg shadow-blue-500/10">
                  <CardBody className="p-4">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg shadow-blue-500/25">
                        <span className="text-white text-sm font-bold">
                          {user.email?.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-white text-sm font-semibold truncate">
                          {user.user_metadata?.name || 'ผู้ดูแลระบบ'}
                        </p>
                        <p className="text-gray-300 text-xs truncate">
                          {user.email}
                        </p>
                      </div>
                    </div>
                    <Button
                      onPress={handleLogout}
                      size="sm"
                      className="w-full bg-gradient-to-r from-red-500/20 to-red-600/20 text-red-300 hover:from-red-500/30 hover:to-red-600/30 border border-red-500/30 hover:border-red-500/50 transition-all duration-300 font-semibold"
                    >
                      ออกจากระบบ
                    </Button>
                  </CardBody>
                </Card>
              </div>
            )}
          </motion.div>

          {/* Main Content */}
          <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'ml-72' : 'ml-16'}`}>
            {/* Header */}
            <header className="bg-black/30 backdrop-blur-md border-b border-blue-500/20 p-6 shadow-lg shadow-blue-500/5">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2">
                    {pathname === '/admin' && 'Dashboard'}
                    {pathname === '/admin/blogs' && 'จัดการบทความ'}
                    {pathname === '/admin/blogs/new' && 'เขียนบทความใหม่'}
                    {pathname.startsWith('/admin/blogs/edit/') && 'แก้ไขบทความ'}
                    {pathname === '/admin/settings' && 'ตั้งค่า'}
                  </h2>
                  <p className="text-gray-300 text-lg">จัดการเนื้อหาและการตั้งค่าของเว็บไซต์อย่างง่ายดาย</p>
                </div>
                <div className="flex items-center gap-3">
                  <Link href="/" target="_blank">
                    <Button
                      variant="bordered"
                      className="border-blue-500/30 text-blue-300 hover:bg-blue-500/10 hover:border-blue-500/50 transition-all duration-300 font-semibold"
                    >
                      ดูเว็บไซต์
                    </Button>
                  </Link>
                  <Link href="/blogs" target="_blank">
                    <Button
                      variant="bordered"
                      className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10 hover:border-purple-500/50 transition-all duration-300 font-semibold"
                    >
                      ดูบทความ
                    </Button>
                  </Link>
                </div>
              </div>
            </header>

            {/* Page Content */}
            <main className="p-8">
              {children}
            </main>
          </div>
        </div>
      </div>
    </AdminProtectedRoute>
  );
}
