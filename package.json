{"name": "nextjs-<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroui/react": "^2.8.1", "@supabase/supabase-js": "^2.52.0", "framer-motion": "^12.23.6", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "quill": "^2.0.3", "react-quill": "^2.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.2", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}