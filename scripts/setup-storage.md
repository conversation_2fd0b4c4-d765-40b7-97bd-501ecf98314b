# Setup Supabase Storage for Blog Images

## 1. Run Database Migrations

First, make sure you've run the database migrations to set up the storage bucket and RLS policies:

### Option A: Using Supabase CLI (Recommended)
```bash
# If you have Supabase CLI installed
supabase db push

# Or apply specific migrations
supabase db push --include-all
```

### Option B: Using Supabase Dashboard
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Run the migration files in order:
   - First run: `supabase/migrations/001_create_blogs_table.sql`
   - Then run: `supabase/migrations/002_setup_storage.sql`

## 2. Verify Storage Bucket Creation

### Check in Supabase Dashboard:
1. Go to **Storage** in your Supabase dashboard
2. You should see a bucket named `blog-images`
3. The bucket should be configured as:
   - **Public**: Yes
   - **File size limit**: 5MB
   - **Allowed MIME types**: image/jpeg, image/png, image/gif, image/webp

### If bucket doesn't exist, create manually:
1. Go to **Storage** → **Create bucket**
2. **Name**: `blog-images`
3. **Public bucket**: ✅ (checked)
4. **File size limit**: `5242880` (5MB in bytes)
5. **Allowed MIME types**: 
   ```
   image/jpeg
   image/png
   image/gif
   image/webp
   ```

## 3. Verify RLS Policies

### Check Storage Policies:
Go to **Storage** → **Policies** and verify these policies exist:

#### For `storage.objects` table:
1. **"Public can view blog images"** (SELECT)
   - Target roles: `public`
   - USING expression: `bucket_id = 'blog-images'`

2. **"Admin can upload blog images"** (INSERT)
   - Target roles: `authenticated`
   - WITH CHECK expression:
     ```sql
     bucket_id = 'blog-images' AND
     auth.role() = 'authenticated' AND (
         (auth.jwt() ->> 'user_metadata')::json ->> 'role' = 'admin' OR
         (auth.jwt() ->> 'app_metadata')::json ->> 'role' = 'admin'
     )
     ```

3. **"Admin can update blog images"** (UPDATE)
   - Target roles: `authenticated`
   - USING expression: Same as INSERT policy

4. **"Admin can delete blog images"** (DELETE)
   - Target roles: `authenticated`
   - USING expression: Same as INSERT policy

### Check Blog Table Policies:
Go to **Authentication** → **Policies** and verify these policies exist for the `blogs` table:

1. **"Anyone can read published blogs"** (SELECT)
   - USING: `published = true`

2. **"Admin can read all blogs"** (SELECT)
   - USING: Admin role check

3. **"Admin can insert blogs"** (INSERT)
   - WITH CHECK: Admin role check

4. **"Admin can update blogs"** (UPDATE)
   - USING: Admin role check

5. **"Admin can delete blogs"** (DELETE)
   - USING: Admin role check

## 4. Test the Setup

### Test Image Upload:
1. Login to admin panel: `/admin/login`
2. Go to create new blog: `/admin/blogs/new`
3. Try uploading an image in the featured image section
4. Verify the image uploads successfully and shows a Supabase Storage URL

### Test Permissions:
1. **As Admin**: Should be able to upload, view, and delete images
2. **As Non-Admin**: Should not be able to upload images
3. **Public**: Should be able to view uploaded images via direct URL

## 5. Environment Variables

Make sure your `.env.local` has the correct Supabase configuration:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 6. Troubleshooting

### Common Issues:

#### "Bucket not found" error:
- Check if the bucket exists in Storage dashboard
- Verify the bucket name is exactly `blog-images`
- Run the storage migration again

#### "Permission denied" error:
- Check if user is logged in as admin
- Verify admin role in user metadata
- Check RLS policies are correctly applied

#### Images not loading:
- Verify bucket is set to public
- Check the generated URL format
- Ensure CORS is properly configured

#### Upload fails silently:
- Check browser console for errors
- Verify file size is under 5MB
- Ensure file type is supported (JPEG, PNG, GIF, WebP)

### Manual Policy Creation:

If policies don't exist, you can create them manually in SQL Editor:

```sql
-- Storage policies
CREATE POLICY "Public can view blog images" ON storage.objects
    FOR SELECT USING (bucket_id = 'blog-images');

CREATE POLICY "Admin can upload blog images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'blog-images' AND
        auth.role() = 'authenticated' AND (
            (auth.jwt() ->> 'user_metadata')::json ->> 'role' = 'admin' OR
            (auth.jwt() ->> 'app_metadata')::json ->> 'role' = 'admin'
        )
    );

-- Add UPDATE and DELETE policies similarly...
```

## 7. Security Notes

### Production Considerations:
1. **File Size Limits**: 5MB limit is enforced
2. **File Type Validation**: Only image types allowed
3. **Admin-Only Uploads**: Only admin users can upload
4. **Public Read Access**: Images are publicly accessible (required for blog display)
5. **Automatic Cleanup**: Consider implementing cleanup for unused images

### Monitoring:
- Monitor storage usage in Supabase dashboard
- Set up alerts for storage quota limits
- Regularly audit uploaded files

The storage system is now secure and ready for production use! 🔒📸
