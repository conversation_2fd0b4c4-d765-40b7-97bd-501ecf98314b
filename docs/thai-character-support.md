# Thai Character Support in Blog Slugs

This document explains how Thai characters are supported in blog URLs and slugs.

## Overview

The TraderBase blog system now fully supports Thai characters in blog URLs, allowing for SEO-friendly URLs like:
- `/blogs/สรุปทุกเรื่อง`
- `/blogs/เริ่มต้นเทรด-forex-สำหรับมือใหม่`
- `/blogs/การเทรด-forex-ที่ดีที่สุด`

## Implementation Details

### 1. Slug Generation (`src/lib/supabase-admin.ts`)

The `generateSlug` function has been updated to preserve Thai characters:

```typescript
generateSlug(title: string): string {
  return (
    title
      .toLowerCase()
      // Keep Thai characters, English letters, numbers, spaces, and hyphens
      .replace(/[^\u0E00-\u0E7F\w\s-]/g, "") // Remove special characters but keep Thai
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single
      .replace(/^-+|-+$/g, "") // Remove leading/trailing hyphens
      .trim()
  );
}
```

**Key Features:**
- **Thai Unicode Range**: `\u0E00-\u0E7F` preserves all Thai characters
- **Mixed Language Support**: Supports Thai + English combinations
- **Special Character Removal**: Removes punctuation but keeps meaningful characters
- **URL-Safe Format**: Converts spaces to hyphens for clean URLs

### 2. Database Function (`supabase/migrations/20241224000003_update_slug_function_thai_support.sql`)

Updated PostgreSQL function to handle Thai characters:

```sql
CREATE OR REPLACE FUNCTION generate_slug(title TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN lower(
        trim(
            regexp_replace(
                regexp_replace(
                    regexp_replace(
                        -- Keep Thai characters, English letters, numbers, spaces, and hyphens
                        regexp_replace(title, '[^\u0E00-\u0E7F\w\s-]', '', 'g'),
                        '\s+', '-', 'g'  -- Replace spaces with hyphens
                    ),
                    '-+', '-', 'g'  -- Replace multiple hyphens with single
                ),
                '^-+|-+$', '', 'g'  -- Remove leading/trailing hyphens
            )
        )
    );
END;
$$ LANGUAGE plpgsql;
```

### 3. URL Handling

#### Frontend URL Encoding
All blog links now properly encode Thai characters:

```typescript
// In blog listing pages
<Link href={`/blogs/${encodeURIComponent(blog.slug)}`}>

// In admin pages
<Link href={`/blogs/${encodeURIComponent(blog.slug)}`} target="_blank">
```

#### URL Decoding in Blog Page
The blog page properly decodes Thai characters from URLs:

```typescript
// In src/app/blogs/[slug]/page.tsx
const slug = decodeURIComponent(params.slug as string);
```

## Examples

### Input → Output Examples

| Title Input | Generated Slug |
|-------------|----------------|
| `สรุปทุกเรื่อง Forex Trading` | `สรุปทุกเรื่อง-forex-trading` |
| `เริ่มต้นเทรด FOREX สำหรับมือใหม่` | `เริ่มต้นเทรด-forex-สำหรับมือใหม่` |
| `การเทรด!@# Forex $%^ ที่ดีที่สุด` | `การเทรด-forex-ที่ดีที่สุด` |
| `How to Trade FOREX ในไทย` | `how-to-trade-forex-ในไทย` |

### URL Examples

| Blog Title | Generated URL |
|------------|---------------|
| `สรุปทุกเรื่อง Forex` | `/blogs/สรุปทุกเรื่อง-forex` |
| `เทคนิคการเทรด` | `/blogs/เทคนิคการเทรด` |
| `Forex Trading Tips` | `/blogs/forex-trading-tips` |

## Browser Compatibility

Modern browsers automatically handle Thai characters in URLs:
- **Chrome**: Full support
- **Firefox**: Full support  
- **Safari**: Full support
- **Edge**: Full support

URLs are automatically encoded/decoded by the browser, so users can:
1. **Copy/paste URLs** with Thai characters
2. **Bookmark pages** with Thai URLs
3. **Share links** on social media
4. **Use browser history** normally

## SEO Benefits

Thai character URLs provide several SEO advantages:

1. **Better User Experience**: URLs match the content language
2. **Improved CTR**: Thai URLs are more recognizable to Thai users
3. **Social Media Friendly**: Thai URLs display properly when shared
4. **Search Engine Optimization**: Google supports Thai characters in URLs

## Testing

To test Thai character support:

1. **Create a blog** with a Thai title like "สรุปทุกเรื่อง Forex"
2. **Check the generated slug** in the admin interface
3. **Visit the blog URL** to ensure it loads correctly
4. **Share the URL** to verify encoding/decoding works
5. **Test in different browsers** for compatibility

## Migration

If you have existing blogs with English-only slugs that should have Thai characters:

1. **Run the database migration** to update the slug function
2. **Optionally update existing slugs** by re-saving blog titles
3. **Test all existing blog URLs** to ensure they still work

## Troubleshooting

### Common Issues

1. **404 Errors**: Ensure URL encoding/decoding is consistent
2. **Broken Links**: Check that all blog links use `encodeURIComponent()`
3. **Database Errors**: Verify the migration was applied successfully
4. **Display Issues**: Confirm Thai fonts are loaded properly

### Debug Steps

1. Check browser developer tools for URL encoding
2. Verify database slug values match expected format
3. Test with different Thai character combinations
4. Confirm server-side routing handles encoded URLs

## Future Enhancements

Potential improvements for Thai character support:

1. **Transliteration Option**: Provide Roman alphabet alternatives
2. **Custom Slug Override**: Allow manual slug editing for special cases
3. **Slug History**: Track slug changes for SEO purposes
4. **Automatic Redirects**: Handle old English slugs to new Thai slugs
