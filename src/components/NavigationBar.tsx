'use client'

import {
  <PERSON><PERSON>,
  <PERSON>vider,
  <PERSON>vbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  NavbarMenu,
  NavbarMenuItem,
  NavbarMenuToggle,
} from "@heroui/react";
import { motion } from "framer-motion";
import { useRouter, usePathname } from "next/navigation";



interface NavigationBarProps {
  activeSection: string;
  isMenuOpen: boolean;
  setIsMenuOpen: (open: boolean) => void;
  scrollToSection: (sectionId: string) => void;
}

export default function NavigationBar({
  activeSection,
  isMenuOpen,
  setIsMenuOpen,
  scrollToSection
}: NavigationBarProps) {
  const router = useRouter();
  const pathname = usePathname();

  const sections = [
    { id: '/', title: 'หน้าหลัก', icon: '●' },
    { id: '/#learn', title: 'เรียนรู้การเทรด FOREX', icon: '●' },
    { id: '/#brokers', title: 'โบรกเกอร์', icon: '●' },
    // { id: '/#systems', title: 'ระบบเทรด', icon: '●' },
    { id: '/blogs', title: 'บทความ', icon: '●' },
    { id: '/#contact', title: 'ติดต่อ', icon: '●' }
  ];

  const handleLogoClick = () => {
    if (pathname === '/') {
      // If we're on the home page, scroll to hero section
      scrollToSection('hero');
    } else {
      // If we're on another page, navigate to home page
      router.push('/');
    }
  };

  const handleScrollToSection = (sectionId: string) => {
    // Handle special case for home page navigation
    if (sectionId === '/') {
      if (pathname === '/') {
        // Already on home page, scroll to hero
        scrollToSection('hero');
      } else {
        // Navigate to home page
        router.push('/');
      }
      return;
    }

    // Handle direct page routes (like /blogs)
    if (sectionId.startsWith('/') && !sectionId.startsWith('/#')) {
      router.push(sectionId);
      return;
    }

    // Handle hash-based navigation (e.g., /#hero, /#learn)
    if (sectionId.startsWith('/#')) {
      const targetSection = sectionId.substring(2); // Remove '/#' prefix

      if (pathname === '/') {
        // Already on home page, scroll to the target section
        scrollToSection(targetSection);
      } else {
        // Navigate to home page with hash
        router.push(sectionId);
      }
      return;
    }

    // Handle regular section IDs
    if (pathname === '/') {
      // On home page, scroll to section
      scrollToSection(sectionId);
    } else {
      // On other pages, navigate to home with hash
      router.push(`/#${sectionId}`);
    }
  };

  // Helper function to check if a section is active
  const isSectionActive = (sectionId: string) => {
    if (sectionId === '/') {
      return pathname === '/' && activeSection === 'hero';
    }
    if (sectionId.startsWith('/') && !sectionId.startsWith('/#')) {
      return pathname === sectionId;
    }
    if (sectionId.startsWith('/#')) {
      return pathname === '/' && activeSection === sectionId.substring(2);
    }
    return pathname === '/' && activeSection === sectionId;
  };
  return (
    <motion.div
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className="fixed top-0 left-0 right-0 z-50"
    >
      <Navbar
        isMenuOpen={isMenuOpen}
        onMenuOpenChange={setIsMenuOpen}
        className="bg-black/30 backdrop-blur-xl border-b border-blue-500/20 transition-all duration-300 hover:bg-black/50 hover:border-blue-500/40"
      >
        <NavbarContent>
          <NavbarMenuToggle
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
            className="sm:hidden text-white"
          />
          <NavbarBrand>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              whileHover={{ scale: 1.05 }}
              className="flex items-center gap-2 cursor-pointer"
              onClick={handleLogoClick}
            >
              <motion.div
                className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center"
                whileHover={{
                  rotate: 360,
                  boxShadow: "0 0 20px rgba(59, 130, 246, 0.5)"
                }}
                transition={{ duration: 0.6 }}
              >
                <span className="text-white font-bold text-sm">T</span>
              </motion.div>
              <motion.span
                className="text-white font-bold text-xl"
                whileHover={{
                  textShadow: "0 0 10px rgba(59, 130, 246, 0.8)"
                }}
              >
                TraderBase
              </motion.span>
            </motion.div>
          </NavbarBrand>
        </NavbarContent>

        <NavbarContent className="hidden sm:flex gap-4" justify="center">
          {sections.map((section, index) => (
            <NavbarItem key={section.id}>
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                whileHover={{ y: -2 }}
              >
                <button
                  className="text-white/80 hover:text-white transition-all duration-300 relative group px-3 py-2 rounded-lg hover:bg-blue-500/10"
                  onClick={() => handleScrollToSection(section.id)}
                >
                  <motion.span
                    whileHover={{
                      textShadow: "0 0 8px rgba(59, 130, 246, 0.8)"
                    }}
                  >
                    {section.title}
                  </motion.span>
                  <motion.div
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-green-500 rounded-full"
                    initial={{ scaleX: 0 }}
                    whileHover={{ scaleX: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                </button>
              </motion.div>
            </NavbarItem>
          ))}
        </NavbarContent>

        <NavbarContent justify="end">
          {/* <NavbarItem>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="ghost"
                className="text-white border-blue-500/50 hover:bg-blue-500/20 hover:border-blue-500/70 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25"
              >
                เข้าสู่ระบบ
              </Button>
            </motion.div>
          </NavbarItem>
          <NavbarItem>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold hover:from-blue-600 hover:to-purple-600 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30"
              >
                เริ่มเทรด
              </Button>
            </motion.div>
          </NavbarItem> */}
        </NavbarContent>

        {/* Mobile Menu */}
        <NavbarMenu className="bg-black/95 backdrop-blur-xl border-r border-blue-500/20">
          <div className="flex flex-col gap-4 mt-8">
            {sections.map((section, index) => (
              <NavbarMenuItem key={section.id}>
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ x: 10 }}
                >
                  <button
                    className={`w-full text-left p-4 rounded-lg transition-all duration-300 flex items-center gap-3 ${isSectionActive(section.id)
                      ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30'
                      : 'text-white/80 hover:text-white hover:bg-blue-500/10'
                      }`}
                    onClick={() => {
                      handleScrollToSection(section.id);
                      setIsMenuOpen(false);
                    }}
                  >
                    <span className="text-xl">{section.icon}</span>
                    <span className="font-medium">{section.title}</span>
                    {isSectionActive(section.id) && (
                      <motion.div
                        className="ml-auto w-2 h-2 bg-blue-400 rounded-full"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3 }}
                      />
                    )}
                  </button>
                </motion.div>
              </NavbarMenuItem>
            ))}

            <Divider className="bg-gray-700 my-4" />

            <NavbarMenuItem>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="space-y-3"
              >
                <Button
                  variant="ghost"
                  className="w-full text-white border-blue-500/50 hover:bg-blue-500/20"
                  onPress={() => setIsMenuOpen(false)}
                >
                  เข้าสู่ระบบ
                </Button>
                <Button
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold"
                  onPress={() => setIsMenuOpen(false)}
                >
                  เริ่มเทรด
                </Button>
              </motion.div>
            </NavbarMenuItem>
          </div>
        </NavbarMenu>
      </Navbar>
    </motion.div>
  );
}
