'use client'

import { useState, useEffect } from "react";
import { Button } from "@heroui/react";
import { motion } from "framer-motion";
import NavigationBar from "@/components/NavigationBar";
import FloatingNavigation from "@/components/FloatingNavigation";
import HeroSection from "@/components/HeroSection";
import ForexBasicsSection from "@/components/ForexBasicsSection";
import BrokersSection from "@/components/BrokersSection";

import FooterSection from "@/components/FooterSection";

export default function Home() {
  const [mounted, setMounted] = useState(false);
  const [livePrice, setLivePrice] = useState(1.0856);
  const [priceChange, setPriceChange] = useState(0.0023);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('hero');
  const [showFloatingNav, setShowFloatingNav] = useState(false);



  useEffect(() => {
    setMounted(true);

    // Handle hash navigation on page load
    const handleHashNavigation = () => {
      const hash = window.location.hash;
      if (hash && hash.length > 1) {
        const sectionId = hash.substring(1); // Remove '#' prefix
        setTimeout(() => {
          scrollToSection(sectionId);
        }, 100); // Small delay to ensure page is fully loaded
      }
    };

    // Handle hash navigation on page load
    handleHashNavigation();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashNavigation);

    // Simulate live forex price updates
    const timer = setInterval(() => {
      const change = (Math.random() - 0.5) * 0.01;
      setLivePrice(prev => +(prev + change).toFixed(4));
      setPriceChange(change);
    }, 3000);

    // Handle scroll for floating navigation
    const handleScroll = () => {
      const scrollY = window.scrollY;
      setShowFloatingNav(scrollY > 300);

      // Update active section based on scroll position
      const homeSections = ['hero', 'learn', 'brokers', 'systems', 'contact'];

      const sectionElements = homeSections.map(sectionId => ({
        id: sectionId,
        element: document.getElementById(sectionId),
        offset: document.getElementById(sectionId)?.offsetTop || 0
      }));

      const currentSection = sectionElements.find((section, index) => {
        const nextSection = sectionElements[index + 1];
        return scrollY >= section.offset - 100 &&
          (!nextSection || scrollY < nextSection.offset - 100);
      });

      if (currentSection) {
        setActiveSection(currentSection.id);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      clearInterval(timer);
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('hashchange', handleHashNavigation);
    };
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  if (!mounted) return null;

  return (
    <div className="min-h-full bg-gradient-to-br from-black via-blue-900/20 to-black overflow-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden -z-10">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse delay-2000"></div>

        {/* Floating Trading Charts */}
        <div className="absolute top-20 right-20 opacity-10">
          <div className="w-32 h-20 border border-purple-500/30 rounded bg-black/20 backdrop-blur-sm"></div>
        </div>
        <div className="absolute bottom-32 left-20 opacity-10">
          <div className="w-24 h-16 border border-blue-500/30 rounded bg-black/20 backdrop-blur-sm"></div>
        </div>
      </div>

      {/* Navigation */}
      <NavigationBar
        activeSection={activeSection}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
        scrollToSection={scrollToSection}
      />

      {/* Floating Table of Contents */}
      <FloatingNavigation
        activeSection={activeSection}
        showFloatingNav={showFloatingNav}
        scrollToSection={scrollToSection}
      />

      {/* Hero Section */}
      <HeroSection livePrice={livePrice} priceChange={priceChange} />

      {/* Learning Steps Section */}
      {/* <LearningStepsSection /> */}

      {/* FOREX Basics FAQ */}
      <ForexBasicsSection />

      {/* See More Button */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.8 }}
        className="flex justify-center mt-12 mb-16"
      >
        <Button
          as="a"
          href="/forex-trading-steps"
          size="lg"
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold px-8 py-4 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <span className="flex items-center gap-2">
            ดูเนื้อหาทั้งหมด 4 Steps
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </span>
        </Button>
      </motion.div>

      {/* Brokers Section */}
      <BrokersSection />

      {/* Trading Systems Section */}
      {/* <TradingSystemsSection /> */}

      {/* Footer */}
      <FooterSection />
    </div>
  );
}