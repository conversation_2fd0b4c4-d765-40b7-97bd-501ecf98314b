'use client'

import { useState, useEffect, useCallback } from "react";
import { useParams } from "next/navigation";
import { motion } from "framer-motion";
import { Card, CardBody, Chip, Button } from "@heroui/react";
import { blogApi, Blog } from "@/lib/supabase";
import Link from "next/link";
import Image from "next/image";
import NavigationBar from "@/components/NavigationBar";
import FooterSection from "@/components/FooterSection";
import BlogContentRenderer from "@/components/BlogContentRenderer";

export default function BlogPage() {
  const params = useParams();
  // Decode the slug to handle Thai characters and special characters
  const slug = decodeURIComponent(params.slug as string);

  const [blog, setBlog] = useState<Blog | null>(null);
  const [relatedBlogs, setRelatedBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeSection] = useState('blog');
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const loadBlog = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const blogData = await blogApi.getBlogBySlug(slug);
      setBlog(blogData);

      // Load related blogs based on tags
      if (blogData.tags.length > 0) {
        const relatedData = await blogApi.getBlogsByTag(blogData.tags[0], 1, 3);
        // Filter out current blog
        const filtered = relatedData.blogs.filter(b => b.id !== blogData.id);
        setRelatedBlogs(filtered);
      }
    } catch (error) {
      console.error('Error loading blog:', error);
      setError('ไม่พบบทความที่ต้องการ');
    } finally {
      setLoading(false);
    }
  }, [slug]);

  useEffect(() => {
    if (slug) {
      loadBlog();
    }
  }, [slug, loadBlog]);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('th-TH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const shareOnFacebook = () => {
    const url = encodeURIComponent(window.location.href);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
  };

  const shareOnTwitter = () => {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(blog?.title || '');
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
  };

  const copyLink = () => {
    navigator.clipboard.writeText(window.location.href);
    // You could add a toast notification here
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-white text-xl">กำลังโหลด...</div>
      </div>
    );
  }

  if (error || !blog) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
        <NavigationBar
          activeSection={activeSection}
          isMenuOpen={isMenuOpen}
          setIsMenuOpen={setIsMenuOpen}
          scrollToSection={scrollToSection}
        />
        <div className="pt-32 px-4 text-center">
          <div className="max-w-2xl mx-auto">
            <h1 className="text-4xl font-bold text-white mb-4">ไม่พบบทความ</h1>
            <p className="text-gray-300 mb-8">บทความที่คุณต้องการอาจถูกลบหรือย้ายไปแล้ว</p>
            <Link href="/blogs">
              <Button className="bg-gradient-to-r from-blue-500 to-green-500 text-white font-semibold px-8 py-3">
                กลับไปหน้าบทความ
              </Button>
            </Link>
          </div>
        </div>
        <FooterSection />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-blue-900/20 to-black overflow-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden -z-10">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Navigation */}
      <NavigationBar
        activeSection={activeSection}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
        scrollToSection={scrollToSection}
      />

      {/* Blog Header */}
      <section className="relative pt-32 pb-16 px-4 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute top-40 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-1/2 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Breadcrumb */}
            <div className="flex items-center gap-2 text-sm text-gray-500 mb-6">
              <Link href="/blogs" className="hover:text-blue-400 transition-colors">
                บทความ
              </Link>
              <span>/</span>
              <span className="text-gray-300">{blog.title}</span>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-6">
              {blog.tags.map((tag) => (
                <Link key={tag} href={`/blogs?tag=${tag}`}>
                  <Chip className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-300 hover:bg-blue-500/30 transition-colors cursor-pointer border border-blue-500/20">
                    {tag}
                  </Chip>
                </Link>
              ))}
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              {blog.title}
            </h1>

            {/* Meta Info */}
            <div className="flex flex-wrap items-center gap-6 text-gray-400 mb-8">
              <div className="flex items-center gap-3">
                {blog.author_avatar && (
                  <Image
                    src={blog.author_avatar}
                    alt={blog.author_name}
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                )}
                <span>{blog.author_name}</span>
              </div>
              <span>{formatDate(blog.published_at || blog.created_at)}</span>
              {blog.reading_time && (
                <span>{blog.reading_time} นาทีในการอ่าน</span>
              )}
              <span>{blog.view_count} views</span>
            </div>

            {/* Featured Image */}
            {blog.featured_image && (
              <div className="relative h-64 md:h-96 rounded-lg overflow-hidden mb-8">
                <Image
                  src={blog.featured_image}
                  alt={blog.title}
                  fill
                  className="object-cover"
                />
              </div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Blog Content */}
      <section className="pb-16 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <Card className="bg-gray-900/30 border border-gray-700/30 backdrop-blur-sm">
              <CardBody className="p-8 md:p-12">
                <BlogContentRenderer content={blog.content} />
              </CardBody>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Share Section */}
      <section className="pb-16 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center"
          >
            <h3 className="text-xl font-semibold text-white mb-6">แชร์บทความนี้</h3>
            <div className="flex justify-center gap-4">
              <Button
                onPress={shareOnFacebook}
                className="bg-blue-600 text-white hover:bg-blue-700"
              >
                Facebook
              </Button>
              <Button
                onPress={shareOnTwitter}
                className="bg-sky-500 text-white hover:bg-sky-600"
              >
                Twitter
              </Button>
              <Button
                onPress={copyLink}
                variant="bordered"
                className="border-white/20 text-white hover:bg-white/10"
              >
                คัดลอกลิงก์
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Related Blogs */}
      {relatedBlogs.length > 0 && (
        <section className="pb-16 px-4">
          <div className="max-w-6xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-3xl font-bold text-white mb-8 text-center"
            >
              บทความที่เกี่ยวข้อง
            </motion.h2>
            <div className="grid md:grid-cols-3 gap-6">
              {relatedBlogs.map((relatedBlog, index) => (
                <motion.div
                  key={relatedBlog.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                >
                  <Link href={`/blogs/${relatedBlog.slug}`}>
                    <Card className="bg-black/20 border border-white/10 hover:border-blue-500/50 transition-all duration-300 cursor-pointer h-full">
                      {relatedBlog.featured_image && (
                        <div className="relative h-48 overflow-hidden">
                          <Image
                            src={relatedBlog.featured_image}
                            alt={relatedBlog.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      <CardBody className="p-6">
                        <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
                          {relatedBlog.title}
                        </h3>
                        <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                          {relatedBlog.excerpt}
                        </p>
                        <div className="flex items-center justify-between text-xs text-gray-400">
                          <span>{formatDate(relatedBlog.published_at || relatedBlog.created_at)}</span>
                          {relatedBlog.reading_time && (
                            <span>{relatedBlog.reading_time} นาที</span>
                          )}
                        </div>
                      </CardBody>
                    </Card>
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      )}

      <FooterSection />
    </div>
  );
}
