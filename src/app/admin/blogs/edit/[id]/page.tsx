'use client'

import { useState, useEffect, useCallback } from "react";
import { useParams, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Textarea,
  Switch,
  Chip
} from "@heroui/react";
import { Blog, BlogContent } from "@/lib/supabase";
import { adminBlogApi, adminBlogUtils } from "@/lib/supabase-admin";
import BlogEditor from "@/components/BlogEditor";
import ImageUpload from "@/components/ImageUpload";
import BlogPreview from "@/components/BlogPreview";

export default function EditBlogPage() {
  const params = useParams();
  const router = useRouter();
  const blogId = params.id as string;

  const [blog, setBlog] = useState<Blog | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    featured_image: '',
    author_name: 'TraderBase Team',
    author_avatar: '',
    published: false,
    featured: false,
    tags: [] as string[],
    meta_title: '',
    meta_description: ''
  });
  const [content, setContent] = useState<BlogContent[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [errors, setErrors] = useState<string[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  const loadBlog = useCallback(async () => {
    try {
      setLoading(true);
      const blogData = await adminBlogApi.getBlogById(blogId);
      setBlog(blogData);

      setFormData({
        title: blogData.title,
        slug: blogData.slug,
        excerpt: blogData.excerpt || '',
        featured_image: blogData.featured_image || '',
        author_name: blogData.author_name,
        author_avatar: blogData.author_avatar || '',
        published: blogData.published,
        featured: blogData.featured,
        tags: blogData.tags,
        meta_title: blogData.meta_title || '',
        meta_description: blogData.meta_description || ''
      });

      setContent(blogData.content);
    } catch (error) {
      console.error('Error loading blog:', error);
      setErrors(['ไม่พบบทความที่ต้องการแก้ไข']);
    } finally {
      setLoading(false);
    }
  }, [blogId]);

  useEffect(() => {
    if (blogId) {
      loadBlog();
    }
  }, [blogId, loadBlog]);

  const handleInputChange = (field: string, value: string | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: string[] = [];

    if (!formData.title.trim()) {
      newErrors.push('กรุณาใส่หัวข้อบทความ');
    }

    if (!formData.slug.trim()) {
      newErrors.push('กรุณาใส่ slug');
    }

    if (content.length === 0) {
      newErrors.push('กรุณาเพิ่มเนื้อหาบทความ');
    }

    const contentValidation = adminBlogUtils.validateBlogContent(content);
    if (!contentValidation.isValid) {
      newErrors.push(...contentValidation.errors);
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleSave = async (publish?: boolean) => {
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);

      // Auto-generate excerpt if not provided
      const excerpt = formData.excerpt || adminBlogUtils.generateExcerpt(content);

      // Calculate reading time
      const wordCount = adminBlogUtils.countWords(content);
      const reading_time = Math.ceil(wordCount / 200); // 200 words per minute

      // Auto-set featured image from content if not provided
      const featured_image = formData.featured_image || adminBlogUtils.extractFirstImage(content) || undefined;

      const updates = {
        ...formData,
        content,
        excerpt,
        reading_time,
        featured_image,
        published: publish !== undefined ? publish : formData.published,
        published_at: publish === true ? new Date().toISOString() :
          publish === false ? undefined :
            blog?.published_at || undefined
      };

      const updatedBlog = await adminBlogApi.updateBlog(blogId, updates);

      if (publish === true) {
        router.push(`/blogs/${updatedBlog.slug}`);
      } else {
        router.push('/admin/blogs');
      }
    } catch (error) {
      console.error('Error updating blog:', error);
      setErrors(['เกิดข้อผิดพลาดในการอัปเดตบทความ']);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('คุณแน่ใจหรือไม่ที่จะลบบทความนี้? การดำเนินการนี้ไม่สามารถย้อนกลับได้')) {
      return;
    }

    try {
      setSaving(true);
      await adminBlogApi.deleteBlog(blogId);
      router.push('/admin/blogs');
    } catch (error) {
      console.error('Error deleting blog:', error);
      setErrors(['เกิดข้อผิดพลาดในการลบบทความ']);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-white text-xl">กำลังโหลด...</div>
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-white mb-4">ไม่พบบทความ</h1>
        <Button
          onPress={() => router.push('/admin/blogs')}
          className="bg-blue-500 text-white hover:bg-blue-600"
        >
          กลับไปหน้าจัดการบทความ
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex justify-between items-center"
      >
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">แก้ไขบทความ</h1>
          <p className="text-gray-400">แก้ไข: {blog.title}</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="bordered"
            className="border-white/20 text-white hover:bg-white/10"
            onPress={() => router.back()}
          >
            ยกเลิก
          </Button>
          <Button
            onPress={handleDelete}
            disabled={saving}
            className="bg-red-500 text-white hover:bg-red-600"
          >
            ลบ
          </Button>
          <Button
            onPress={() => setShowPreview(!showPreview)}
            className="bg-purple-600 text-white hover:bg-purple-700"
          >
            {showPreview ? 'แก้ไข' : 'ดูตัวอย่าง'}
          </Button>
          <Button
            onPress={() => handleSave()}
            disabled={saving}
            className="bg-gray-600 text-white hover:bg-gray-700"
          >
            {saving ? 'กำลังบันทึก...' : 'บันทึก'}
          </Button>
          {!formData.published && (
            <Button
              onPress={() => handleSave(true)}
              disabled={saving}
              className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold"
            >
              {saving ? 'กำลังเผยแพร่...' : 'เผยแพร่'}
            </Button>
          )}
          {formData.published && (
            <Button
              onPress={() => handleSave(false)}
              disabled={saving}
              className="bg-yellow-500 text-white hover:bg-yellow-600"
            >
              {saving ? 'กำลังยกเลิก...' : 'ยกเลิกเผยแพร่'}
            </Button>
          )}
        </div>
      </motion.div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="bg-red-500/10 border border-red-500/20">
            <CardBody className="p-4">
              <h3 className="text-red-400 font-semibold mb-2">กรุณาแก้ไขข้อผิดพลาดต่อไปนี้:</h3>
              <ul className="list-disc list-inside text-red-300 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Preview Mode */}
      {showPreview ? (
        <BlogPreview
          title={formData.title}
          excerpt={formData.excerpt}
          featuredImage={formData.featured_image}
          authorName={formData.author_name}
          authorAvatar={formData.author_avatar}
          tags={formData.tags}
          content={content}
          readingTime={blog?.reading_time || Math.ceil(adminBlogUtils.countWords(content) / 200)}
        />
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="bg-black/20 border border-white/10">
                <CardHeader>
                  <h3 className="text-xl font-semibold text-white">ข้อมูลพื้นฐาน</h3>
                </CardHeader>
                <CardBody className="space-y-4">
                  <Input
                    label="หัวข้อบทความ"
                    placeholder="ใส่หัวข้อบทความ..."
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    classNames={{
                      input: "bg-black/20 text-white",
                      inputWrapper: "bg-black/20 border border-white/20"
                    }}
                  />

                  <Input
                    label="Slug (URL)"
                    placeholder="url-friendly-slug"
                    value={formData.slug}
                    onChange={(e) => handleInputChange('slug', e.target.value)}
                    classNames={{
                      input: "bg-black/20 text-white",
                      inputWrapper: "bg-black/20 border border-white/20"
                    }}
                  />

                  <Textarea
                    label="สรุปบทความ"
                    placeholder="เขียนสรุปสั้นๆ ของบทความ..."
                    value={formData.excerpt}
                    onChange={(e) => handleInputChange('excerpt', e.target.value)}
                    classNames={{
                      input: "bg-black/20 text-white",
                      inputWrapper: "bg-black/20 border border-white/20"
                    }}
                    minRows={3}
                  />

                  <div>
                    <label className="block text-white text-sm font-medium mb-2">
                      รูปภาพหน้าปก
                    </label>
                    <ImageUpload
                      currentImage={formData.featured_image}
                      onImageUpload={(imageUrl) => handleInputChange('featured_image', imageUrl)}
                    />
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            {/* Content Editor */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="bg-black/20 border border-white/10">
                <CardHeader>
                  <h3 className="text-xl font-semibold text-white">เนื้อหาบทความ</h3>
                </CardHeader>
                <CardBody>
                  <BlogEditor
                    initialContent={content}
                    onChange={setContent}
                  />
                </CardBody>
              </Card>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Blog Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="bg-black/20 border border-white/10">
                <CardHeader>
                  <h3 className="text-lg font-semibold text-white">สถิติ</h3>
                </CardHeader>
                <CardBody className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">ยอดเข้าชม:</span>
                    <span className="text-white font-semibold">{blog.view_count}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">เวลาอ่าน:</span>
                    <span className="text-white font-semibold">{blog.reading_time} นาที</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">สร้างเมื่อ:</span>
                    <span className="text-white font-semibold">
                      {new Date(blog.created_at).toLocaleDateString('th-TH')}
                    </span>
                  </div>
                  {blog.published_at && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">เผยแพร่เมื่อ:</span>
                      <span className="text-white font-semibold">
                        {new Date(blog.published_at).toLocaleDateString('th-TH')}
                      </span>
                    </div>
                  )}
                </CardBody>
              </Card>
            </motion.div>

            {/* Publish Settings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <Card className="bg-black/20 border border-white/10">
                <CardHeader>
                  <h3 className="text-lg font-semibold text-white">การเผยแพร่</h3>
                </CardHeader>
                <CardBody className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-white">เผยแพร่</span>
                    <Switch
                      isSelected={formData.published}
                      onValueChange={(value) => handleInputChange('published', value)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-white">บทความแนะนำ</span>
                    <Switch
                      isSelected={formData.featured}
                      onValueChange={(value) => handleInputChange('featured', value)}
                    />
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            {/* Tags */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <Card className="bg-black/20 border border-white/10">
                <CardHeader>
                  <h3 className="text-lg font-semibold text-white">แท็ก</h3>
                </CardHeader>
                <CardBody className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      placeholder="เพิ่มแท็ก..."
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && addTag()}
                      classNames={{
                        input: "bg-black/20 text-white",
                        inputWrapper: "bg-black/20 border border-white/20"
                      }}
                    />
                    <Button
                      onPress={addTag}
                      className="bg-blue-500 text-white hover:bg-blue-600"
                    >
                      เพิ่ม
                    </Button>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag) => (
                      <Chip
                        key={tag}
                        onClose={() => removeTag(tag)}
                        className="bg-blue-500/20 text-blue-300"
                      >
                        {tag}
                      </Chip>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          </div>
        </div>
      )}
    </div>
  );
}
