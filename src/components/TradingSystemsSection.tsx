'use client'

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Accordion, AccordionItem, But<PERSON> } from "@heroui/react";
import { motion } from "framer-motion";
import { useReadableAccordionItem } from "@/components/ReadableAccordionItem";

export default function TradingSystemsSection() {
  const item1Props = useReadableAccordionItem("1", "1. เริ่มต้นเทรดยังไง", "step4");
  const item2Props = useReadableAccordionItem("2", "2. วิธีอยู่รอดในตลาด Forex", "step4");
  const item3Props = useReadableAccordionItem("3", "3. วิธีดูกราฟ Forex เบื้องต้น", "step4");
  const item4Props = useReadableAccordionItem("4", "4. หลักการวิเคราะห์โดยรวม", "step4");
  const item5Props = useReadableAccordionItem("5", "5. หลักการวิเคราะห์ทางเทคนิค", "step4");
  const item6Props = useReadableAccordionItem("6", "6. วิเคราะห์ปัจจัยพื้นฐาน", "step4");
  const item7Props = useReadableAccordionItem("7", "7. วิเคราะห์ข่าว", "step4");
  const item8Props = useReadableAccordionItem("8", "8. ทำความรู้จักคู่สกุลเงินต่างๆ", "step4");
  const item9Props = useReadableAccordionItem("9", "9. การใช้งานอินดิเตอร์แบบเจาะลึก", "step4");
  const item10Props = useReadableAccordionItem("10", "10. การสร้างระบบเทรด Forex", "step4");
  const item11Props = useReadableAccordionItem("11", "11. การทดสอบระบบเทรด", "step4");

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 1.0 }}
      className="max-w-4xl mx-auto"
    >
      <Card className="bg-black/40 backdrop-blur-md border border-green-500/20">
        <CardHeader>
          <h3 className="text-2xl font-bold text-white">Step 4 : การซื้อขาย, วิเคราะห์ Forex</h3>
        </CardHeader>
        <CardBody>
          <Accordion variant="splitted" className="px-0">
            <AccordionItem {...item1Props}>
              <div className="text-gray-300 space-y-4">
                <div className="space-y-2">
                  <p>• คาดการณ์ราคาขึ้น &gt; เปิด Long (Buy)</p>
                  <p>• คาดการณ์ราคาลง &gt; เปิด Short (Sell)</p>
                  <p>• ถ้าถูกทาง = กำไร แต่ถ้าผิดทาง = ขาดทุน</p>
                  <p>• ดังนั้นเราต้องคาดการณ์ทิศทางการแกว่งตัวของราคาให้ได้ เพื่อสร้างกำไรในการเทรด</p>
                </div>

                <div className="text-center">
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-4xl mb-2">📈</div>
                    <p className="text-sm">ทำกำไร Forex</p>
                    <p className="text-xs text-gray-400">881x319 Trading Diagram</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <p>• หลักการดูเหมือนง่าย แพ้-ชนะ 50/50 แต่เอาเข้าจริงคนกว่า 85% โดยรวมแพ้ใน 2 ปีแรก</p>
                  <div className="ml-4 space-y-1 text-sm">
                    <p>- เนื่องจากจากเงื่อนไขทางด้าน Winrate + Risk/Reward + จำนวน lot ที่ออก + อารมณ์</p>
                    <p>- มักออกมาในแบบ ชนะมาตลอด ยังไม่ได้ถอนเงินใช้อยู่ๆก็ล้างพอร์ต เสียทั้งเวลาและเงิน ที่ได้มาคือความรู้ที่ว่า</p>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                  <p className="text-yellow-300 font-semibold text-center italic">
                    &quot;ไม่น่ารีบเลย … รู้งี้ถอนเงินมาใช้ หรือศึกษาให้ดีก่อนใส่เงินจำนวนมากดีกว่า&quot;
                  </p>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item2Props}>
              <div className="text-gray-300 space-y-4">
                <p>3 องค์ประกอบในการอยู่เทรดแบบยั่งยืน</p>

                <div className="space-y-3">
                  <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                    <h4 className="text-blue-400 font-semibold mb-2">1) วิธีการเทรด</h4>
                    <p className="text-sm">มีกลยุทธ์การเทรดที่กำไร (สำคัญมาก ต้องศึกษา ต้องฝึกฝน ไม่มีทางได้มาเพราะโชคช่วย)</p>
                  </div>

                  <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                    <h4 className="text-green-400 font-semibold mb-2">2) การบริหารความเสี่ยง</h4>
                    <p className="text-sm">ควบคุมหน้าตัก (เงินทุน) ให้ดี (ถ้าไม่มี เก่งแค่ไหนก็แพ้หมดตูดสักวัน)</p>
                  </div>

                  <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <h4 className="text-purple-400 font-semibold mb-2">3) จิตวิทยา</h4>
                    <p className="text-sm">มีวินัยในการเทรด ไว้คุม 2 ข้อข้างบน เป็นตัววัดความสำเร็จอย่างยั่งยืนในระยะยาว</p>
                  </div>
                </div>

                <div className="text-center">
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-4xl mb-2">🛡️</div>
                    <p className="text-sm">อยู่รอดในตลาด Forex</p>
                    <p className="text-xs text-gray-400">872x365 Survival Diagram</p>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item3Props}>
              <div className="text-gray-300 space-y-4">
                <div className="space-y-2">
                  <p>• กราฟบน Forex สะท้อนให้เห็นถึง <strong>&quot;แรงซื้อ และ &quot;แรงขาย&quot;</strong></p>
                  <p>• สามารถนำมาคาดการณ์ทิศทางในอนาคตได้</p>
                </div>

                <div>
                  <h4 className="text-blue-400 font-semibold mb-3">แท่งเทียน</h4>
                  <div className="space-y-2 text-sm">
                    <p>• บนกราฟจะประกอบด้วยแท่งเทียนแต่ละแท่งเรียงต่อกัน</p>
                    <p>• 1 แท่งเทียนจะแทน 1 หน่วย (แล้วแต่จะเลือกดู Time Fram (TF) ไหน เช่น ถ้าเลือก TF Day (D1) 1 แท่งแปลว่า 1 วัน)</p>
                    <p>• 1 แท่งเทียนประกอบด้วย 4 ข้อมูล คือ Open, High, Low และ Close</p>
                    <p>• แท่งเทียน<span className="text-green-400 font-semibold">สีเขียว</span> = ราคาปิด &gt; ราคาเปิด (<span className="text-green-400 font-semibold">ราคาขึ้น</span>)</p>
                    <p>• แท่งเทียน<span className="text-red-400 font-semibold">สีแดง</span> = ราคาปิด &lt; ราคาเปิด (<span className="text-red-400 font-semibold">ราคาลง</span>)</p>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                  <p className="text-yellow-300 text-sm">
                    บนมือถือ MT4/MT5 จะใช้สีดำ เป็นแท่งเทียนที่ปิดบวก (ปิด &gt; เปิด) และใช้สีขาว เป็นแท่งเทียนที่ปิดลบ (ปิด &lt; เปิด)
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="p-3 bg-gray-800/30 rounded-lg">
                      <div className="text-3xl mb-2">🕯️</div>
                      <p className="text-sm">แท่งเทียน</p>
                      <p className="text-xs text-gray-400">322x213</p>
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="p-3 bg-gray-800/30 rounded-lg">
                      <div className="text-3xl mb-2">📊</div>
                      <p className="text-sm">แท่งเทียนใน MT4/MT5</p>
                      <p className="text-xs text-gray-400">395x176</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p>• <strong>High</strong> = ราคาสูงสุด</p>
                    <p>• <strong>Low</strong> = ราคาต่ำสุด</p>
                  </div>
                  <div>
                    <p>• <strong>Open</strong> = ราคาเปิด</p>
                    <p>• <strong>Close</strong> = ราคาปิด</p>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item4Props}>
              <div className="text-gray-300 space-y-4">
                <p>การวิเคราะห์มี 3 รูปแบบหลัก คือ</p>

                <div className="space-y-3">
                  <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                    <h4 className="text-blue-400 font-semibold mb-2">วิเคราะห์ทางเทคนิค (Technical Analysis)</h4>
                    <p className="text-sm">ดูกราฟ ใช้ Indicator หรือ Chart pattern</p>
                  </div>

                  <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                    <h4 className="text-green-400 font-semibold mb-2">วิเคราะห์ปัจจัยพื้น (Fundamental Analysis)</h4>
                    <p className="text-sm">ดูปัจจัยพื้นฐาน หรือดัชนีชี้วัดต่างๆทางเศรษฐกิจ ภาพรวมใหญ่ๆ มีผลระยะยาว เช่น การเมือง สงคราม GDP การเกิดและการจัดการโรคระบาด เป็นต้น</p>
                  </div>

                  <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <h4 className="text-purple-400 font-semibold mb-2">วิเคราะห์ข่าว</h4>
                    <p className="text-sm">ดูข่าวเศรษฐกิจแล้ววิเคราะห์แนวโน้มจากข่าว ซึ่งจะทำให้เกิดเทรน 15 นาที – 1 วัน หรือมากกว่านั้น แล้วแต่สภาพความแรงของข่าว</p>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                  <p className="text-yellow-300 text-sm">
                    * ไม่จำเป็นต้องรู้ทั้ง 3 รูปแบบนี้ ขอเพียงเก่งรูปแบบใดรูปแบบหนึ่ง ก็สามารถสร้างกำไรในการเทรด Forex ได้แล้ว แต่หากใครที่รู้ทั้ง 3 รูปแบบ ก็จะยิ่งเสริมประสิทธิภาพการเทรดให้สูงขึ้น
                  </p>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item5Props}>
              <div className="text-gray-300 space-y-4">
                <div className="space-y-2">
                  <p>• การวิเคราะห์ทางเทคนิค คือการดูกราฟ</p>
                  <p>• คาดการณ์ทิศทางในอนาคตจากพฤติกรรมของราคาในอดีต</p>
                  <p>• ทั้งการดู รูปแบบแท่งเทียน และ Indicator</p>
                </div>

                <div className="text-center">
                  <Button
                    className="bg-blue-600 text-white hover:bg-blue-700"
                    size="sm"
                  >
                    การวิเคราะห์การเทรดด้วย Technical Analysis
                  </Button>
                </div>

                <div className="text-center">
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-4xl mb-2">📊</div>
                    <p className="text-sm">กราฟทางเทคนิค</p>
                    <p className="text-xs text-gray-400">600x323</p>
                    <p className="text-xs text-gray-500 mt-1">รูปแสดงตัวอย่างการวิเคราะห์ทางเทคนิค โดยใช้ Indicator ต่างๆช่วย ทำนายทิศทางของกราฟ</p>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item6Props}>
              <div className="text-gray-300 space-y-4">
                <div className="space-y-2">
                  <p>• ข้อมูลและข่าวสารต่างๆที่มีผลต่อทิศทางของค่าเงิน</p>
                  <p>• อาทิ ตัวเลข GDP,เงินเฟ้อ, Non-farm, ตัวเลขการจ้างงาน และอื่นๆ</p>
                  <p>• ปัจจัยทางการเมือง : การปรับขึ้นลงของอัตราดอกเบี้ย, เสถียรภาพการเมือง และ นโยบายของรัฐ</p>
                </div>

                <div className="text-center">
                  <Button
                    className="bg-green-600 text-white hover:bg-green-700"
                    size="sm"
                  >
                    การวิเคราะห์ปัจจัยพื้นฐาน คือ อะไร ?
                  </Button>
                </div>

                <div className="text-center">
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-4xl mb-2">⚔️</div>
                    <p className="text-sm">ปัจจัยพื้นฐาน</p>
                    <p className="text-xs text-gray-400">640x427 War/Politics Image</p>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item7Props}>
              <div className="text-gray-300 space-y-4">
                <p>• ทั้งการประกาศตัวเลขเศรษฐกิจ, การเมือง, สงคราม เหล่านี้เราสามารถนำมาคาดการณ์การเคลื่อนไหวของราคาในอนาคตได้</p>

                <div className="mt-4 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <h4 className="text-blue-400 font-semibold mb-2">💡 Tip</h4>
                  <p className="text-sm">เว็บไซต์ที่เทรดเดอร์หลายคนใช้ติดตามการประกาศตัวเลขเศรษฐกิจ คือ forexfactory.com</p>
                </div>

                <div className="text-center">
                  <Button
                    className="bg-purple-600 text-white hover:bg-purple-700"
                    size="sm"
                  >
                    วิธีใช้ข่าวจาก forexfactory.com
                  </Button>
                </div>

                <div className="text-center">
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-4xl mb-2">📰</div>
                    <p className="text-sm">Forexfactory</p>
                    <p className="text-xs text-gray-400">1086x578</p>
                    <p className="text-xs text-gray-500 mt-1">ตารางข่าวจาก Forexfactory ซึ่งเทรดเดอร์สายข่าว นิยมใช้เทรดในช่วงมีข่าวเพราะกราฟจะผันผวน และหาจังหวะทำกำไรได้</p>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item8Props}>
              <div className="text-gray-300 space-y-4">
                <p>คู่สกุลเงินหลักที่เทรดกันส่วนมาก มีอยู่ 7 คู่สกุลเงิน</p>

                <div className="grid md:grid-cols-2 gap-2 text-sm">
                  <div className="space-y-1">
                    <p>1. <strong>EURUSD</strong> : ยูโร / ดอลลาร์สหรัฐ</p>
                    <p>2. <strong>USDJPY</strong> : ดอลลาร์สหรัฐ / เยน (ญี่ปุ่น)</p>
                    <p>3. <strong>GBPUSD</strong> : ปอนด์ (อังกฤษ) / ดอลลาร์สหรัฐ</p>
                    <p>4. <strong>AUDUSD</strong> : ดอลลาร์ออสเตรเลีย / ดอลลาร์สหรัฐ</p>
                  </div>
                  <div className="space-y-1">
                    <p>5. <strong>USDCAD</strong> : ดอลลาร์สหรัฐ / ดอลลาร์แคนาดา</p>
                    <p>6. <strong>USDCHF</strong> : ดอลลาร์สหรัฐ / สวิสฟรังค์ (สวิตเซอร์แลนด์)</p>
                    <p>7. <strong>NZDUSD</strong> : ดอลลาร์นิวซีแลน / ดอลลาร์สหรัฐ</p>
                  </div>
                </div>

                <div className="text-center">
                  <Button
                    className="bg-orange-600 text-white hover:bg-orange-700"
                    size="sm"
                  >
                    Major Currency Pairs (คู่เงินหลัก) คือ อะไร ?
                  </Button>
                </div>

                <div className="text-center">
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-4xl mb-2">💱</div>
                    <p className="text-sm">Major Currency Pairs Forex</p>
                    <p className="text-xs text-gray-400">840x560</p>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item9Props}>
              <div className="text-gray-300 space-y-4">
                <p>Indicator เป็นเครื่องมือการวิเคราะห์ทางเทคนิค ที่ใช้ประกอบการตัดสินใจในการเทรด Forex การใช้งาน Indicator <strong>เป็นมากกว่าการนำ Indicator เข้าไปใส่ในกราฟแล้วทำการเทรดตามสัญญาณที่ให้</strong></p>

                <p>แต่การใช้ Indicator นั้นต้องรู้จักสังเกตุรายละเอียดมากมาย พร้อมทั้งฝึกฝนอยู่เสมอจนชำนาญ สิ่งที่ต้องศึกษาได้แก่</p>

                <div className="space-y-2 text-sm">
                  <p>• <strong>ที่มาและหลักการของ Indicator นั้นๆ</strong> (ถ้ารู้ลึกซึ้งจะใช้งานได้เต็มประสิทธิภาพ)</p>
                  <p>• <strong>การใช้งาน และการติดตั้ง Indicator ใน MT4-5</strong></p>
                  <p>• <strong>การใช้งาน Indicator ร่วมกับเครื่องมืออื่นๆ</strong></p>
                </div>

                <div className="text-center">
                  <Button
                    className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold"
                    size="lg"
                  >
                    เลือกเรียนรู้ Indicator แต่ละชนิด คลิ๊ก
                  </Button>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item10Props}>
              <div className="text-gray-300 space-y-4">
                <div className="space-y-2">
                  <p>• ระบบเทรด &gt; <strong>&quot;เทรดเป็นระบบ&quot; ก็เหมือนมวยมืออาชีพ ย่อมชนะมวยวัด</strong></p>
                  <p>• เป็นการสร้างกลยุทธ์การเทรดขึ้นมา จากการ<strong>ตกผลึก</strong>องค์ความรู้ในการเทรดต่างๆ จนเหมาะสมกับตัวเอง</p>
                  <p>• อาจใช้ Price action หรือ Indicator ต่างๆ</p>
                  <p>• ระบบเทรด เปรียบเสมือนมีแผนที่ในการเดินทาง</p>
                  <p>• ช่วยให้การเทรดของเรามีประสิทธิภาพอย่างมาก</p>
                  <p>• จะตัดเรื่องอารมณ์ในการเทรดออกไป</p>
                  <p>• องค์ประกอบที่สำคัญในการเอาชนะตลาด</p>
                  <p>• มีความแหลมคมใน จุดเข้า จุดออก (เป้าหมาย / ตัดขาดทุน) และ Size ในการเทรด</p>
                  <p>• ระบบเทรดที่ดี ควรผ่านการทดสอบมาก่อน</p>
                  <p>• ระบบเทรดอัตโนมัติ = EA</p>
                </div>

                <div className="text-center">
                  <Button
                    className="bg-gradient-to-r from-green-500 to-blue-500 text-white font-semibold"
                    size="lg"
                  >
                    แจกระบบเทรด
                  </Button>
                </div>

                <div className="mt-6 p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20">
                  <h4 className="text-green-400 font-semibold mb-3 text-center">🎯 สำเร็จแล้ว! คุณได้เรียนรู้ครบทั้ง 4 Steps</h4>
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p>✅ <strong>Step 1:</strong> พื้นฐาน Forex</p>
                      <p>✅ <strong>Step 2:</strong> เลือกโบรกเกอร์</p>
                    </div>
                    <div>
                      <p>✅ <strong>Step 3:</strong> เครื่องมือเทรด</p>
                      <p>✅ <strong>Step 4:</strong> ระบบเทรด</p>
                    </div>
                  </div>
                  <div className="text-center mt-4">
                    <p className="text-blue-300 font-semibold">พร้อมเริ่มต้นการเทรด Forex อย่างมืออาชีพแล้ว!</p>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item11Props}>
              <div className="text-gray-300 space-y-4">
                <div className="space-y-2">
                  <p>• ทำการเทรดตามระบบ และเก็บข้อมูลผ่าน MyFxbook</p>
                  <p>• ดูสถิติผลตอบแทนย้อนหลัง อย่างน้อย 1 ปี</p>
                  <p>• ปีที่ 2 ในการทดสอบเพื่อสร้างความเฉียบคมของระบบเทรด</p>
                  <p>• หากอดีตกำไร ถือว่าระบบนั้นอยู่ในเกณฑ์ดี</p>
                  <p>• แต่หากอดีตขาดทุน มีโอกาสที่อนาคตจะขาดทุนสูง</p>
                </div>

                <div className="mt-4 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <h4 className="text-blue-400 font-semibold mb-3">📊 สิ่งที่ต้องดูเพิ่มเติม</h4>
                  <div className="grid md:grid-cols-2 gap-3 text-sm">
                    <div className="space-y-2">
                      <div className="p-2 bg-gray-800/30 rounded">
                        <p><strong className="text-green-400">Expected Payoff</strong></p>
                        <p className="text-xs text-gray-400">ผลตอบแทนคาดหวัง ต่อ 1 การเทรด</p>
                      </div>
                      <div className="p-2 bg-gray-800/30 rounded">
                        <p><strong className="text-blue-400">Winrate</strong></p>
                        <p className="text-xs text-gray-400">อัตราการชนะ</p>
                      </div>
                      <div className="p-2 bg-gray-800/30 rounded">
                        <p><strong className="text-red-400">Max. Drawdown</strong></p>
                        <p className="text-xs text-gray-400">การขาดทุนสูงสุดที่เกิดขึ้น</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="p-2 bg-gray-800/30 rounded">
                        <p><strong className="text-purple-400">Profit factor</strong></p>
                        <p className="text-xs text-gray-400">ผลรวมกำไร / ผลรวมขาดทุน</p>
                      </div>
                      <div className="p-2 bg-gray-800/30 rounded">
                        <p><strong className="text-yellow-400">Total trades</strong></p>
                        <p className="text-xs text-gray-400">จำนวนเทรดทั้งหมด</p>
                      </div>
                      <div className="p-2 bg-gray-800/30 rounded">
                        <p><strong className="text-orange-400">Consecutive Win/Loss</strong></p>
                        <p className="text-xs text-gray-400">การแพ้/ชนะ ติดต่อกัน</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <Button
                    className="bg-blue-600 text-white hover:bg-blue-700"
                    size="sm"
                  >
                    MyFxbook คืออะไร?
                  </Button>
                </div>

                <div className="mt-6 p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20">
                  <h4 className="text-green-400 font-semibold mb-3 text-center">🎯 ครบทั้ง 4 Steps แล้ว!</h4>
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p>✅ <strong>Step 1:</strong> พื้นฐาน Forex (10 หัวข้อ)</p>
                      <p>✅ <strong>Step 2:</strong> เลือกโบรกเกอร์ (10 หัวข้อ)</p>
                    </div>
                    <div>
                      <p>✅ <strong>Step 3:</strong> เครื่องมือเทรด (11 หัวข้อ)</p>
                      <p>✅ <strong>Step 4:</strong> ระบบเทรด (11 หัวข้อ)</p>
                    </div>
                  </div>
                  <div className="text-center mt-4">
                    <p className="text-blue-300 font-semibold">รวม 42 หัวข้อ - การศึกษา Forex ครบวงจร!</p>
                    <p className="text-green-300 text-sm mt-2">พร้อมเริ่มต้นการเทรด Forex อย่างมืออาชีพแล้ว! 🚀</p>
                  </div>
                </div>
              </div>
            </AccordionItem>
          </Accordion>
        </CardBody>
      </Card>
    </motion.div>
  );
}
