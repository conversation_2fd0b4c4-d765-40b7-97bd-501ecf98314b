-- Rollback Migration: Remove uploads bucket and policies
-- Created: 2024-12-24
-- Description: Rollback script to remove the uploads bucket and all associated policies

-- Drop all policies for uploads bucket
DROP POLICY IF EXISTS "Public read access for uploads bucket" ON storage.objects;
DROP POLICY IF EXISTS "Admin upload access for uploads bucket" ON storage.objects;
DROP POLICY IF EXISTS "Admin update access for uploads bucket" ON storage.objects;
DROP POLICY IF EXISTS "Admin delete access for uploads bucket" ON storage.objects;

-- Drop indexes
DROP INDEX IF EXISTS idx_storage_objects_bucket_id;
DROP INDEX IF EXISTS idx_storage_objects_owner;
DROP INDEX IF EXISTS idx_storage_objects_bucket_owner;

-- Delete all objects in the uploads bucket first
DELETE FROM storage.objects WHERE bucket_id = 'uploads';

-- Delete the uploads bucket
DELETE FROM storage.buckets WHERE id = 'uploads';

-- Note: RLS remains enabled on storage.objects as it may be used by other buckets
-- If you want to disable <PERSON><PERSON> completely (not recommended), uncomment the line below:
-- ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;
