'use client'

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, Accordion, AccordionItem, Button } from "@heroui/react";
import { motion } from "framer-motion";
import { useReadableAccordionItem } from "@/components/ReadableAccordionItem";

export default function TradingToolsSection() {
  const item1Props = useReadableAccordionItem("1", "1. คู่มือ MT4 (วิธีลงโปรแกรม วิธีใช้งานเบื้องต้น)", "step3");
  const item2Props = useReadableAccordionItem("2", "2. คู่มือ MT5 (วิธีลง platform วิธีการใช้งานเบื้องต้น)", "step3");
  const item3Props = useReadableAccordionItem("3", "3. การเทรดผ่านมือถือ", "step3");
  const item4Props = useReadableAccordionItem("4", "4. การเทรดผ่านเว็บไซต์ (ไม่ต้องลงโปรแกรม)", "step3");
  const item5Props = useReadableAccordionItem("5", "5. รู้จักกับอินดิเคเตอร์", "step3");
  const item6Props = useReadableAccordionItem("6", "6. การเทรดด้วย Robot (EA)", "step3");
  const item7Props = useReadableAccordionItem("7", "7. การสร้าง EA", "step3");
  const item8Props = useReadableAccordionItem("8", "8. การทดสอบ EA", "step3");
  const item9Props = useReadableAccordionItem("9", "9. การซื้อ EA", "step3");
  const item10Props = useReadableAccordionItem("10", "10. การรัน EA", "step3");
  const item11Props = useReadableAccordionItem("11", "11. สรุปเครื่องมือเทรด สู่ Step ถัดไป", "step3");

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.8 }}
      className="max-w-4xl mx-auto"
    >
      <Card className="bg-black/40 backdrop-blur-md border border-purple-500/20">
        <CardHeader>
          <h3 className="text-2xl font-bold text-white">Step 3 : เครื่องมือเทรด Forex</h3>
        </CardHeader>
        <CardBody>
          <Accordion variant="splitted" className="px-0">
            <AccordionItem {...item1Props}>
              <div className="text-gray-300 space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                    <h4 className="text-blue-400 font-semibold mb-2">📹 Exness MT4 Tutorial</h4>
                    <div className="aspect-video bg-gray-800 rounded-lg flex items-center justify-center mb-2">
                      <div className="text-center">
                        <div className="text-4xl mb-2">▶️</div>
                        <p className="text-sm">YouTube Video Tutorial</p>
                      </div>
                    </div>
                    <Button
                      className="bg-blue-600 text-white hover:bg-blue-700 w-full"
                      size="sm"
                    >
                      ดู Tutorial Exness MT4
                    </Button>
                  </div>

                  <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <h4 className="text-purple-400 font-semibold mb-2">📹 XM MT4 Tutorial</h4>
                    <div className="aspect-video bg-gray-800 rounded-lg flex items-center justify-center mb-2">
                      <div className="text-center">
                        <div className="text-4xl mb-2">▶️</div>
                        <p className="text-sm">YouTube Video Tutorial</p>
                      </div>
                    </div>
                    <Button
                      className="bg-purple-600 text-white hover:bg-purple-700 w-full"
                      size="sm"
                    >
                      ดู Tutorial XM MT4
                    </Button>
                  </div>
                </div>

                <div className="mt-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                      <h4 className="text-green-400 font-semibold mb-2">✅ ข้อดีของการ &quot;เทรดผ่าน PC&quot;</h4>
                      <div className="text-sm space-y-1">
                        <p>• จอใหญ่ ทำให้เห็นมุมมองของกราฟ ตลอดจนสามารถใช้เครื่องมือช่วยเทรดต่างๆได้ดีกว่า <strong>การหาสัญญาณเข้าเทรดง่ายกว่า</strong></p>
                        <p>• <strong>ใช้เครื่องมือช่วยเทรดได้เต็มประสิทธิภาพมากกว่า</strong> ไม่ว่าจะเป็นการตีเส้น, การใช้ Indicator ต่างๆ, การดูข่าวประกอบ, หรือใช้ EA เทรด</p>
                        <p>• เพิ่มหน้าจอได้หลายจอ หลายคู่เงิน, การเปรียบเทียบ Time frame ได้สะดวกกว่า</p>
                      </div>
                    </div>

                    <div className="p-3 bg-red-500/10 rounded-lg border border-red-500/20">
                      <h4 className="text-red-400 font-semibold mb-2">❌ ข้อเสียของการ &quot;เทรดผ่าน PC&quot;</h4>
                      <div className="text-sm space-y-1">
                        <p>• <strong>พกพาไม่ได้สะดวก:</strong> สำหรับ Notebook หรือ Tablet แต่ PC ตั้งโต๊ะ นี่ไม่สามารถเคลือนย้ายได้เลย</p>
                        <p>• <strong>จำกัดสถานที่และเวลา:</strong> เฉพาะเมื่อพร้อม &gt; ถ้าเป็นมือถือ แม้แต่เข้าห้องน้ำ นั่งรถอยู่ก็สามารถเทรดได้</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center mt-4">
                  <Button
                    className="bg-purple-600 text-white hover:bg-purple-700"
                    size="lg"
                  >
                    เปิดบัญชี XM
                  </Button>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item2Props}>
              <div className="text-gray-300 space-y-4">
                <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <h4 className="text-blue-400 font-semibold mb-2">📹 Exness MT5 Tutorial</h4>
                  <div className="aspect-video bg-gray-800 rounded-lg flex items-center justify-center mb-2">
                    <div className="text-center">
                      <div className="text-4xl mb-2">▶️</div>
                      <p className="text-sm">YouTube Video Tutorial</p>
                      <p className="text-xs text-gray-400">Exness MT5 Platform Guide</p>
                    </div>
                  </div>
                  <Button
                    className="bg-blue-600 text-white hover:bg-blue-700 w-full"
                    size="sm"
                  >
                    ดู Tutorial Exness MT5
                  </Button>
                </div>

                <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                  <h4 className="text-yellow-400 font-semibold mb-2">💡 เกี่ยวกับ MT5</h4>
                  <div className="text-sm space-y-1">
                    <p>• MT5 เป็น platform รุ่นใหม่กว่า MT4</p>
                    <p>• มีฟีเจอร์เพิ่มเติมและการวิเคราะห์ที่ดีกว่า</p>
                    <p>• รองรับการเทรดหลายสินทรัพย์มากกว่า MT4</p>
                    <p>• Interface ที่ทันสมัยและใช้งานง่ายกว่า</p>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item3Props}>
              <div className="text-gray-300 space-y-4">
                <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <h4 className="text-blue-400 font-semibold mb-2">📹 การเทรด Forex ผ่านมือถือ - Exness</h4>
                  <div className="aspect-video bg-gray-800 rounded-lg flex items-center justify-center mb-2">
                    <div className="text-center">
                      <div className="text-4xl mb-2">📱</div>
                      <p className="text-sm">Mobile Trading Tutorial</p>
                      <p className="text-xs text-gray-400">1020x574 Video</p>
                    </div>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                    <h4 className="text-green-400 font-semibold mb-2">✅ ข้อดีของการ &quot;เทรดผ่านมือถือ&quot;</h4>
                    <div className="text-sm">
                      <p>• <strong>สะดวก:</strong> สามารถเทรดได้ทุกที่ที่มีสัญญาณเน็ต</p>
                    </div>
                  </div>

                  <div className="p-3 bg-red-500/10 rounded-lg border border-red-500/20">
                    <h4 className="text-red-400 font-semibold mb-2">❌ ข้อเสียของการ &quot;เทรดบนมือถือ&quot;</h4>
                    <div className="text-sm space-y-1">
                      <p>• การมองแนวโน้มราคา ทำได้ยากกว่าเทรดผ่าน PC มาก เนื่องจาก ความเสียเปรียบทางด้าน</p>
                      <div className="ml-4 space-y-1">
                        <p>- การเห็นมุมมองของกราฟ (จอเล็กมาก)</p>
                        <p>- การเปรียบเทียบระหว่าง Time frame</p>
                        <p>- การหาสัญญาณเข้าเทรด</p>
                        <p>- การดูข่าวประกอบ</p>
                        <p>- การวิเคราะห์ Indicator ที่ใช้ร่วมในการเทรด</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                  <p className="text-yellow-300 text-sm italic">
                    จะไม่ชัดเจนเท่า PC &gt; (เว้นแต่คุณจะเก่งเทคนิคเทรดกราฟเปล่าเอามากๆ)
                  </p>
                </div>

                <div className="mt-4 p-3 bg-red-600/10 rounded-lg border border-red-600/20">
                  <h4 className="text-red-300 font-semibold mb-2">⚠️ คำเตือนสำคัญ</h4>
                  <div className="text-sm space-y-1">
                    <p>• เทรดมือถือเป็นสัญญาณของความรีบเร่งเทรด หรือความไม่พร้อมในการเทรด เช่น เดินทาง ติดงาน ฯลฯ ในแง่จิตวิทยาการเทรด ถือเป็นการนำตัวเองไปสู่จุดที่เสียเปรียบ</p>
                    <p>• คนที่ล้างพอร์ต หรือเทรดผิดทางจำนวนไม่น้อยมีสาเหตุจากการเทรดผ่านมือถือ</p>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item4Props}>
              <div className="text-gray-300 space-y-4">
                <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <h4 className="text-blue-400 font-semibold mb-2">🌐 Web Trading Platform</h4>
                  <p className="text-sm">การเทรดผ่านเว็บบราวเซอร์โดยไม่ต้องติดตั้งโปรแกรม</p>
                </div>

                <div className="mt-4 p-3 bg-red-500/10 rounded-lg border border-red-500/20">
                  <h4 className="text-red-400 font-semibold mb-2">⚠️ จุดอ่อนของการเทรดผ่านเว็บ</h4>
                  <div className="text-sm space-y-1">
                    <p>• การเทรดผ่านเว็บ จะผ่านเว็บบราวเซอร์ ทำให้เมื่อเทียบกับการเทรดผ่าน Platform MT4 จะมีความหน่วงๆมากกว่า</p>
                    <p>• เทรดไม่ลื่นไหล ออกคำสั่งได้ช้ากว่าเล็กน้อย</p>
                    <p>• อันตรายต่อการเทรดช่วงข่าวมากกว่า โดยเฉพาะถ้าคอมหรือเน็ตคุณไม่แรง</p>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                  <h4 className="text-yellow-400 font-semibold mb-2">💡 ข้อแนะนำ</h4>
                  <p className="text-sm">
                    แนะนำให้ใช้ MT4/MT5 แทนการเทรดผ่านเว็บ เพื่อประสิทธิภาพที่ดีกว่าและความเสถียรในการเทรด
                  </p>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item5Props}>
              <div className="text-gray-300 space-y-4">
                <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20">
                  <h4 className="text-purple-400 font-semibold mb-2">📊 อินดิเคเตอร์ (Indicator) คืออะไร</h4>
                  <p className="text-sm">
                    อินดิเคเตอร์ (Indicator) คือ เครื่องมือทางสถิติ ชี้วัดถึงข้อมูลต่างๆ เกี่ยวกับราคาที่ใช้ในการเทรด Forex สามารถนำไปคาดการณ์ทิศทางราคาในอนาคตได้
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="text-blue-400 font-semibold">📈 Indicator ยอดนิยม</h4>
                    <div className="space-y-1 text-sm">
                      <p>• <strong>เส้นค่าเฉลี่ย</strong> (Moving Average)</p>
                      <p>• <strong>RSI</strong> (Relative Strength Index)</p>
                      <p>• <strong>MACD</strong> (Moving Average Convergence Divergence)</p>
                      <p>• <strong>Stochastic</strong></p>
                    </div>
                  </div>

                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-center">
                      <div className="text-4xl mb-2">📊</div>
                      <p className="text-sm">Indicator Example</p>
                      <p className="text-xs text-gray-400">Chart with Indicators</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Button
                    className="bg-purple-600 text-white hover:bg-purple-700 w-full"
                    size="sm"
                  >
                    Indicator คือ อะไร ?
                  </Button>
                  <Button
                    className="bg-blue-600 text-white hover:bg-blue-700 w-full"
                    size="sm"
                  >
                    Indicator Forex ที่ต้องรู้
                  </Button>
                </div>

                <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                  <h4 className="text-yellow-400 font-semibold mb-2">💡 คำแนะนำการใช้ Indicator</h4>
                  <div className="text-sm space-y-1">
                    <p>• Indicator (อินดี้) ที่ใช้ จะใช้แตกต่างกันไปตามเทคนิคการเทรด หรือ &quot;ระบบเทรด&quot;</p>
                    <p>• ปกติจะใช้ไม่เกินสามตัวที่เหมาะสมจริงๆ</p>
                    <p>• การใช้อินดี้มากเกินไป แทนที่จะดี กลับทำให้งงมากกว่า</p>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item6Props}>
              <div className="text-gray-300 space-y-4">
                <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                  <h4 className="text-green-400 font-semibold mb-2">🤖 Expert Advisors (EA) คืออะไร</h4>
                  <div className="text-sm space-y-1">
                    <p>• Expert Adviors หรือ <strong>EA</strong></p>
                    <p>• เป็นการสร้าง Bot ในการช่วยเทรดแบบอัตโนมัติ</p>
                    <p>• สามารถรันบน MT4/MT5</p>
                    <p>• ไม่ต้องเฝ้าจอ ปล่อยให้ EA รันเทรดเอง</p>
                  </div>
                </div>

                <div className="text-center">
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-6xl mb-2">🤖</div>
                    <p className="text-sm">Expert Advisors (EA)</p>
                    <p className="text-xs text-gray-400">Automated Trading Robot</p>
                  </div>
                </div>

                <div className="text-center">
                  <Button
                    className="bg-green-600 text-white hover:bg-green-700"
                    size="sm"
                  >
                    อ่านเพิ่มเติมเกี่ยวกับ EA
                  </Button>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="p-3 bg-red-500/10 rounded-lg border border-red-500/20">
                    <h4 className="text-red-400 font-semibold mb-2">⚠️ จุดอ่อนของ EA</h4>
                    <div className="text-sm space-y-1">
                      <p>• <strong>มีน้อยตัวมากที่สามารถรองรับตลาดที่เปลี่ยนแปลงไปในช่วงต่างๆได้เป็นเวลาปีๆ หรือหลายปี</strong></p>
                      <p>• และยังผูกติดกับการ<strong>ตั้งค่า EA ที่ต้อง Smart พอ</strong> แปลว่า EA ก็มีสิทธิ์ล้างพอร์ตได้ ผู้ใช้ก็ต้องรู้จักมันให้ดีมากๆ สามารถใช้มันได้เต็มประสิทธิภาพ</p>
                    </div>
                  </div>

                  <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                    <h4 className="text-blue-400 font-semibold mb-2">✅ ข้อดีของ EA</h4>
                    <div className="text-sm space-y-1">
                      <p>• แต่ก็นับเป็นวิธีการที่<strong>เร่งความสำเร็จในการเทรดให้เร็วขึ้น</strong></p>
                      <p>• ผู้ใช้ EA <strong>ควรเทรดเป็นแล้วระดับหนึ่ง</strong> ไม่ใช่ไม่เป็นเลย</p>
                    </div>
                  </div>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item7Props}>
              <div className="text-gray-300 space-y-4">
                <p>วิธีการสร้าง EA สามารถทำได้หลายวิธี คือ</p>

                <div className="space-y-3">
                  <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                    <h4 className="text-blue-400 font-semibold mb-2">1. เขียนขึ้นมาเอง</h4>
                    <p className="text-sm">(เขียนบน MetaEditor ด้วยภาษา MQL ที่อยู่ใน MT4 และ MT5)</p>
                  </div>

                  <div className="p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                    <h4 className="text-yellow-400 font-semibold mb-2">2. จ้างเขียน</h4>
                    <div className="text-sm space-y-1">
                      <p className="text-red-300">• ต้องยอมรับเรื่อง คนเขียนจะรู้ระบบการเทรดของเรา</p>
                      <p>• ถ้าคนเขียนไม่เคยเทรด หรือยังเทรดไม่เชี่ยวชาญ อาจเจอปัญหาล่าช้า ทำงานไม่ได้ดังใจ หรือทิ้งงาน</p>
                    </div>
                  </div>

                  <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                    <h4 className="text-green-400 font-semibold mb-2">3. ร่วมมือกับค่ายเขียน EA</h4>
                    <div className="text-sm space-y-1">
                      <p>แล้วแบ่งผลประโยชน์ร่วมกัน</p>
                      <p>• ขจัดปัญหาทิ้งงาน</p>
                      <p>• มีการอัพเดทออย่างต่อเนื่อง อันนี้สำคัญเพราะ EA ดีๆต้องพัฒนาอย่างต่อเนื่องทั้งตัว EA และระบบเทรดคู่ EA (การตั้งค่า) อย่างต่อเนื่อง ไม่ต่ำกว่า 2 ปี</p>
                    </div>
                  </div>

                  <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <h4 className="text-purple-400 font-semibold mb-2">4. ใช้โปรแกรมช่วยเขียน</h4>
                    <div className="text-sm space-y-1">
                      <p>เช่น Fxdreema, FXPRO Quant</p>
                      <p>• ง่ายสะดวก ไม่ต้องเสียเวลาในการเรียนเขียน Code</p>
                      <p>• ยังต้องศึกษาการเขียนผ่านโปรแกรมดังกล่าว คนเขียนโค๊ดเป็นแล้ว อาจไม่เลือกวิธีนี้</p>
                    </div>
                  </div>

                  <div className="p-3 bg-gray-500/10 rounded-lg border border-gray-500/20">
                    <h4 className="text-gray-400 font-semibold mb-2">5. ซื้อ EA จากที่ต่างๆ</h4>
                    <p className="text-sm text-blue-300">(ต้องดูให้ดี)</p>
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-4xl mb-2">🤖</div>
                </div>

                <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                  <p className="text-yellow-300 text-sm">
                    * การสร้าง EA นอกจากจะต้องมีทีมพัฒนาแล้ว ต้องมีทีม Data since ที่ทดสอบระบบ ทั้ง <strong>Back Test &amp; Forward Test</strong>
                  </p>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item8Props}>
              <div className="text-gray-300 space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                    <h4 className="text-blue-400 font-semibold mb-2">📊 การทำ Backtest</h4>
                    <div className="text-sm space-y-1">
                      <p>• เราสามารถทดสอบ EA ของเราว่า มีประสิทธิภาพเป็นอย่างไร ด้วยการทำการ Backtest ย้อนหลัง</p>
                      <p>• สามารถเช็คผลตอบแทนย้อนหลังในอดีตภายใต้เงื่อนไขเวลา และ setting อื่นๆของเงื่อนไขการเทรดของ EA ได้</p>
                      <p>• ว่าถ้ารัน EA ของเรา กับคู่สกุลเงินที่จะลงทุน ในอดีต + การตั้งค่าในรูปแบบต่างๆกันผลตอบแทนที่ได้จะเป็นอย่างไร &gt; <strong>เพื่อหารูปแบบการเทรดที่ดีที่สุด</strong></p>
                      <div className="ml-4 space-y-1">
                        <p>- ปลอดภัยสูงสุด</p>
                        <p>- กำไรสูงสุดภายใต้เงื่อนไข สมดุลของความปลอดภัย</p>
                      </div>
                    </div>
                  </div>

                  <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                    <h4 className="text-green-400 font-semibold mb-2">🔬 การทำ Forward Test</h4>
                    <div className="text-sm space-y-1">
                      <p>• คือทดสอบระบบด้วยการเทรดจริง โดยใช้กราฟแบบ RealTime ทั้งเงินจริงหรือเงิน Demo (เงินจริงชัดเจนที่สุด)</p>
                      <p>• เพื่อใช้ดูเสถียรภาพ, หา Bug, การกินพลังงานของเครื่อง, ผลของของบัญชีประเภทต่างๆ, ผลของ VPS ที่ใช้, ผลของ slippage กับ spread ฯลฯ</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <h4 className="text-white font-semibold mb-2">วิธีการ Backtest</h4>
                    <div className="text-sm space-y-1">
                      <p><strong>ใน MT4:</strong> เมื่อโหลด EA มาแล้ว ให้เปิด View / Strategy Tester</p>
                      <p><strong>ใน MT5:</strong> เมื่อโหลด EA มาแล้ว ให้<strong>คลิ๊กขวา</strong>บน EA แล้วกดปุ่ม Test</p>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-3">
                    <div className="p-2 bg-gray-800/30 rounded text-center">
                      <div className="text-3xl mb-1">📊</div>
                      <p className="text-xs">Back Test EA MT4</p>
                    </div>
                    <div className="p-2 bg-gray-800/30 rounded text-center">
                      <div className="text-3xl mb-1">📈</div>
                      <p className="text-xs">Backtest Report Example</p>
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <Button
                    className="bg-blue-600 text-white hover:bg-blue-700"
                    size="sm"
                  >
                    การ Backtest ระบบเทรด
                  </Button>
                </div>

                <div className="mt-4 p-3 bg-red-500/10 rounded-lg border border-red-500/20">
                  <h4 className="text-red-400 font-semibold mb-2">⚠️ ข้อสำคัญ</h4>
                  <p className="text-sm">
                    <strong>* Forward Test คือของจริง !! EA มากกว่า 80% ได้ผลดีใน Backtest แต่ล้มเหลวใน Forward Test</strong>
                  </p>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item9Props}>
              <div className="text-gray-300 space-y-4">
                <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20">
                  <h4 className="text-purple-400 font-semibold mb-2">🛒 ตลาดซื้อขาย EA ที่ใหญ่ที่สุด</h4>
                  <p className="text-sm mb-2">ตลาดซื้อขาย EA ที่ใหญ่ที่สุดคือ MQL5</p>
                  <Button
                    className="bg-purple-600 text-white hover:bg-purple-700 w-full"
                    size="sm"
                  >
                    เยี่ยมชม MQL5 Market
                  </Button>
                </div>

                <div className="text-center">
                  <div className="p-3 bg-gray-800/30 rounded-lg">
                    <div className="text-4xl mb-2">🛍️</div>
                    <p className="text-sm">EA Market</p>
                    <p className="text-xs text-gray-400">600x245 Market Screenshot</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="text-blue-400 font-semibold">💡 คำแนะนำในการซื้อ EA</h4>
                  <div className="space-y-2 text-sm">
                    <p>• ควรเลือกซื้อ EA ที่เหมาะสม ทั้งราคา และ กลยุทธ์</p>
                    <p>• พิจารณาถึงความน่าเชื่อถือ</p>
                    <p>• มีบางตัวที่ Free สามารถทดลองใช้ก่อนได้</p>
                    <p>• EA ที่มีผู้ใช้งานจำนวนมากมาแชร์การตั้งค่า หรือมีห้องกลุ่มผู้ใช้งาน ย่อมได้เปรียบ</p>
                    <div className="ml-4">
                      <p>- EA ที่ตั้งค่าสูตรโรงงาน อาจได้ผลไม่ดีเท่า สูตรที่มีการปรับอย่างเข้าใจของผู้ใช้งานจริง</p>
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                  <h4 className="text-yellow-400 font-semibold mb-2">🎯 สรุป Step 3</h4>
                  <div className="text-sm space-y-1">
                    <p>• เลือกแพลตฟอร์มที่เหมาะสม (MT4/MT5/Mobile/Web)</p>
                    <p>• เรียนรู้การใช้ Indicator อย่างมีประสิทธิภาพ</p>
                    <p>• พิจารณาการใช้ EA สำหรับการเทรดอัตโนมัติ</p>
                    <p>• ทดสอบเครื่องมือก่อนใช้งานจริง</p>
                  </div>
                </div>

                <div className="text-center mt-6">
                  <div className="text-4xl mb-2">🎯</div>
                  <p className="text-blue-300 font-semibold">พร้อมสำหรับ Step 4: ระบบเทรด</p>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item10Props}>
              <div className="text-gray-300 space-y-4">
                <h4 className="text-blue-400 font-semibold mb-3">📋 ขั้นตอนการรัน EA</h4>

                <div className="space-y-4">
                  <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                    <h5 className="text-blue-300 font-semibold mb-2">1. เปิด Data Folder</h5>
                    <p className="text-sm">ไปที่เมนู File จากนั้นคลิ๊ก &apos;Open Data Folder&apos;</p>
                    <div className="mt-2 p-2 bg-gray-800/30 rounded text-center">
                      <div className="text-2xl mb-1">📁</div>
                      <p className="text-xs">File Menu Screenshot</p>
                      <p className="text-xs text-gray-400">326x218</p>
                    </div>
                  </div>

                  <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                    <h5 className="text-green-300 font-semibold mb-2">2. เปิดโฟลเดอร์ MQL</h5>
                    <p className="text-sm">เปิดโฟลเดอร์ &apos;MQL5&apos; หรือ &apos;MQL4&apos;</p>
                    <div className="mt-2 p-2 bg-gray-800/30 rounded text-center">
                      <div className="text-2xl mb-1">📂</div>
                      <p className="text-xs">MQL Folder Screenshot</p>
                      <p className="text-xs text-gray-400">652x289</p>
                    </div>
                  </div>

                  <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20">
                    <h5 className="text-purple-300 font-semibold mb-2">3. เปิดโฟลเดอร์ Experts</h5>
                    <p className="text-sm">เปิดโฟลเดอร์ &apos;Experts&apos;</p>
                    <div className="mt-2 p-2 bg-gray-800/30 rounded text-center">
                      <div className="text-2xl mb-1">📁</div>
                      <p className="text-xs">Experts Folder Screenshot</p>
                      <p className="text-xs text-gray-400">623x290</p>
                    </div>
                  </div>

                  <div className="p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                    <h5 className="text-yellow-300 font-semibold mb-2">4. วางไฟล์ EA</h5>
                    <p className="text-sm">นำไฟล์ EA (ที่ประกอบด้วย &apos;.ex5&apos; และ &apos;.mq5&apos;) มาวางในโฟลเดอร์นี้</p>
                    <div className="mt-2 p-2 bg-gray-800/30 rounded text-center">
                      <div className="text-2xl mb-1">📄</div>
                      <p className="text-xs">EA Files Screenshot</p>
                      <p className="text-xs text-gray-400">662x222</p>
                    </div>
                  </div>

                  <div className="p-3 bg-orange-500/10 rounded-lg border border-orange-500/20">
                    <h5 className="text-orange-300 font-semibold mb-2">5. เปิด MT5 และตรวจสอบ EA</h5>
                    <p className="text-sm">เมื่อเปิดโปรแกรม MT5 ขึ้นมา จะปรากฏตัว EA ที่เราลง</p>
                    <div className="mt-2 p-2 bg-gray-800/30 rounded text-center">
                      <div className="text-2xl mb-1">🤖</div>
                      <p className="text-xs">EA in MT5 Screenshot</p>
                      <p className="text-xs text-gray-400">269x248</p>
                    </div>
                  </div>

                  <div className="p-3 bg-red-500/10 rounded-lg border border-red-500/20">
                    <h5 className="text-red-300 font-semibold mb-2">6. รัน EA และเปิด AutoTrading</h5>
                    <div className="text-sm space-y-1">
                      <p>ให้กด ตัว EA ที่เราจะรันค้างไว้ แล้วเอาลงไปวางในกราฟ</p>
                      <p>จากนั้นกด &apos;AutoTrading&apos; ให้เป็นสีเขียว</p>
                      <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                        <div className="p-2 bg-green-600/20 rounded text-center">
                          <div className="text-green-400">🟢 สีเขียว</div>
                          <p>EA รันอยู่</p>
                        </div>
                        <div className="p-2 bg-red-600/20 rounded text-center">
                          <div className="text-red-400">🔴 สีแดง</div>
                          <p>EA ไม่ได้รัน</p>
                        </div>
                      </div>
                    </div>
                    <div className="mt-2 p-2 bg-gray-800/30 rounded text-center">
                      <div className="text-2xl mb-1">⚡</div>
                      <p className="text-xs">AutoTrading Button Screenshot</p>
                      <p className="text-xs text-gray-400">473x214</p>
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                  <h4 className="text-green-400 font-semibold mb-2">✅ สำเร็จ!</h4>
                  <p className="text-sm">
                    เมื่อทำตามขั้นตอนครบแล้ว EA จะเริ่มทำงานอัตโนมัติและเทรดตามระบบที่กำหนดไว้
                  </p>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem {...item11Props}>
              <div className="text-gray-300 space-y-4">
                <div className="space-y-2">
                  <p>• การเลือกเครื่องมือเทรดที่เหมาะสมเป็นสิ่งสำคัญต่อความสำเร็จในการเทรด</p>
                  <p>• แต่ละแพลตฟอร์มมีข้อดีข้อเสียที่แตกต่างกัน ควรเลือกให้เหมาะกับสไตล์การเทรด</p>
                  <p>• การใช้ Indicator และ EA ต้องมีความรู้และประสบการณ์เพียงพอ</p>
                </div>

                <div className="grid md:grid-cols-3 gap-4">
                  <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20 text-center">
                    <div className="text-3xl mb-2">💻</div>
                    <h4 className="text-blue-400 font-semibold mb-1">แพลตฟอร์ม</h4>
                    <p className="text-xs">MT4/MT5/Mobile/Web</p>
                  </div>
                  <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20 text-center">
                    <div className="text-3xl mb-2">📊</div>
                    <h4 className="text-purple-400 font-semibold mb-1">เครื่องมือ</h4>
                    <p className="text-xs">Indicators & Analysis</p>
                  </div>
                  <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20 text-center">
                    <div className="text-3xl mb-2">🤖</div>
                    <h4 className="text-green-400 font-semibold mb-1">อัตโนมัติ</h4>
                    <p className="text-xs">Expert Advisors (EA)</p>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                  <h4 className="text-yellow-400 font-semibold mb-2">💡 สิ่งที่ได้เรียนรู้ใน Step 3</h4>
                  <div className="text-sm space-y-1">
                    <p>✅ การเลือกและใช้งานแพลตฟอร์มเทรด (MT4/MT5)</p>
                    <p>✅ ข้อดีข้อเสียของการเทรดผ่าน PC, มือถือ, และเว็บ</p>
                    <p>✅ การใช้ Indicator เพื่อวิเคราะห์ตลาด</p>
                    <p>✅ การสร้าง ทดสอบ และใช้งาน Expert Advisors (EA)</p>
                    <p>✅ การติดตั้งและรัน EA บนแพลตฟอร์ม</p>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <h4 className="text-blue-400 font-semibold mb-2">🎯 เตรียมพร้อมสำหรับ Step 4</h4>
                  <p className="text-sm">
                    ขั้นตอนต่อไปจะเป็นการเรียนรู้เกี่ยวกับระบบเทรด (Trading System) ซึ่งเป็นหัวใจสำคัญของการเทรดที่ประสบความสำเร็จ
                  </p>
                </div>

                <div className="text-center mt-6">
                  <div className="text-4xl mb-2">🚀</div>
                  <p className="text-blue-300 font-semibold">พร้อมสำหรับ Step 4: ระบบเทรด</p>
                </div>
              </div>
            </AccordionItem>
          </Accordion>
        </CardBody>
      </Card>
    </motion.div>
  );
}
