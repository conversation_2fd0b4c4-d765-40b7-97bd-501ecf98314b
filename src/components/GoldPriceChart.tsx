'use client'

import { <PERSON>, CardBody, CardHeader } from "@heroui/react";
// import console from "console";
import { motion } from "framer-motion";
import { useState, useEffect, useRef, useMemo } from "react";

interface GoldPrice {
  price: number;
  change: number;
  changePercent: number;
  timestamp: number;
  high24h: number;
  low24h: number;
  volume: string;
}



// Note: Using dynamic field detection instead of fixed interface

export default function GoldPriceChart() {
  const [goldPrice, setGoldPrice] = useState<GoldPrice | null>(null);

  const [priceHistory, setPriceHistory] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dataSource, setDataSource] = useState<string>(''); // Track data source
  const [renderKey, setRenderKey] = useState(0); // Force re-render trigger
  const [hoverData, setHoverData] = useState<{ x: number; y: number; price: number; day: number } | null>(null);
  const [containerWidth, setContainerWidth] = useState(400); // Dynamic container width
  const isLoadingRef = useRef(false);
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // Binance API configuration (no API key required)
  const GOLD_SYMBOL = 'PAXGUSDT'; // PAX Gold - gold-backed cryptocurrency (1 PAXG = 1 troy ounce of gold)

  // Format volume for display
  const formatVolume = (volume: string): string => {
    const num = parseFloat(volume);
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Test API connectivity with detailed debugging
  const testAPI = async () => {
    try {
      console.log('🧪 Testing API connectivity...');
      const testUrl = '/api/gold/historical?symbol=PAXGUSDT&interval=1d&limit=5';
      console.log('🔗 Test URL:', testUrl);

      const response = await fetch(testUrl);
      console.log('📡 Test response status:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Test API error response:', errorText);
        return false;
      }

      const data = await response.json();
      console.log('🧪 API test response:', data);
      console.log('✅ API test successful');
      return true;
    } catch (error) {
      console.error('🧪 API test failed with error:', error);
      return false;
    }
  };

  // Fetch 30 days of historical data from Binance
  const fetchHistoricalData = async () => {
    if (isLoadingRef.current) {
      console.log('⚠️ Already loading, skipping...');
      return;
    }

    try {
      isLoadingRef.current = true;
      setError(null);

      console.log('📅 Fetching 30 days of historical data from Binance...');
      console.log('📊 Symbol:', GOLD_SYMBOL, '(PAX Gold - 1 PAXG = 1 troy ounce of gold)');

      // Test API first
      console.log('🔍 Testing API connectivity before main fetch...');
      const apiWorking = await testAPI();
      if (!apiWorking) {
        throw new Error('API connectivity test failed - server may be down');
      }

      // Fetch 30 days of daily klines (candlestick data) via our API route
      const url = `/api/gold/historical?symbol=${GOLD_SYMBOL}&interval=1d&limit=30`;
      console.log('🔗 Fetching main data from URL:', url);

      const response = await fetch(url);
      console.log('📡 Main API response status:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Main API error response:', errorText);
        throw new Error(`API Error ${response.status}: ${response.statusText}`);
      }

      const responseData = await response.json();
      console.log('📊 API response:', responseData);

      // Handle the new response format with data wrapper
      const rawData = responseData.data || responseData;
      const actualSymbol = responseData.symbol || GOLD_SYMBOL;

      console.log('📊 Using symbol:', actualSymbol, 'with', rawData.length, 'data points');

      if (!Array.isArray(rawData) || rawData.length === 0) {
        throw new Error('No historical data available from Binance');
      }

      // Process historical data - extract closing prices
      console.log('📈 Raw data sample:', JSON.stringify(rawData[0], null, 2));
      console.log('📈 Raw data structure check:', {
        isArray: Array.isArray(rawData),
        length: rawData.length,
        firstItemType: typeof rawData[0],
        firstItemKeys: rawData[0] ? Object.keys(rawData[0]) : 'no keys'
      });

      // Handle different possible data structures
      let goldPrices: number[] = [];

      if (Array.isArray(rawData) && rawData.length > 0) {
        // Check if it's Binance kline format (array of arrays)
        if (Array.isArray(rawData[0])) {
          console.log('📊 Processing Binance kline format (array of arrays)');
          goldPrices = (rawData as unknown[][]).map((kline: unknown[]) => {
            const closePrice = parseFloat(String(kline[4])); // Index 4 is close price in kline format
            console.log('� Kline item:', kline[4], '-> parsed:', closePrice);
            return closePrice;
          });
        }
        // Check if it's object format
        else if (typeof rawData[0] === 'object' && rawData[0] && 'close' in rawData[0]) {
          console.log('� Processing object format with close property');
          goldPrices = (rawData as { close: string }[]).map((item) => parseFloat(item.close));
        }
        // Try to find price in various possible formats
        else {
          console.log('� Trying to find price in unknown format');
          goldPrices = (rawData as Record<string, unknown>[]).map((item) => {
            // Try different possible price fields
            const price = item.close || item.c || (item as unknown as unknown[])[4] || item.price;
            return parseFloat(String(price));
          });
        }
      }

      console.log('📈 Processed prices sample:', goldPrices.slice(0, 5));
      console.log('📈 Processed', goldPrices.length, 'historical data points');

      // Ensure we have valid price data
      const validPrices = goldPrices.filter(price => !isNaN(price) && price > 0 && isFinite(price));
      console.log('✅ Valid prices:', validPrices.length, 'out of', goldPrices.length);
      console.log('🥇 Raw price range:', validPrices.length > 0 ?
        `${Math.min(...validPrices).toFixed(2)} - ${Math.max(...validPrices).toFixed(2)}` : 'No valid prices');

      // Check if prices look like PAX Gold (should be around $2000+) or something else
      const avgPrice = validPrices.reduce((sum, price) => sum + price, 0) / validPrices.length;
      console.log('📊 Average price:', avgPrice.toFixed(2));
      console.log('💰 Price analysis:', {
        symbol: actualSymbol,
        avgPrice: avgPrice.toFixed(2),
        priceRange: `${Math.min(...validPrices).toFixed(2)} - ${Math.max(...validPrices).toFixed(2)}`,
        looksLikeGold: avgPrice > 1500 ? 'Yes (>$1500)' : 'No (<$1500)',
        samplePrices: validPrices.slice(0, 5).map(p => p.toFixed(2))
      });

      if (validPrices.length === 0) {
        console.error('❌ No valid price data found. Raw data:', rawData);
        throw new Error('No valid price data found - check API response format');
      }

      setPriceHistory(validPrices);
      setRenderKey(prev => prev + 1); // Force re-render
      console.log('✅ Set price history with', validPrices.length, 'valid prices');

      // Get current price and 24h statistics via our API route
      const statsResponse = await fetch(
        `/api/gold/ticker?symbol=${GOLD_SYMBOL}`
      );

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        console.log('📊 Binance 24h stats:', statsData);

        // Try different field names for price data
        const currentPrice = parseFloat(
          statsData.c || statsData.lastPrice || statsData.price || statsData.close || '0'
        );
        const priceChange = parseFloat(
          statsData.p || statsData.priceChange || '0'
        );
        const priceChangePercent = parseFloat(
          statsData.P || statsData.priceChangePercent || '0'
        );
        const high24h = parseFloat(
          statsData.h || statsData.highPrice || statsData.high || '0'
        );
        const low24h = parseFloat(
          statsData.l || statsData.lowPrice || statsData.low || '0'
        );

        console.log('💰 Current price analysis:', {
          symbol: actualSymbol,
          currentPrice: currentPrice.toFixed(2),
          change: priceChange.toFixed(2),
          changePercent: priceChangePercent.toFixed(2),
          high24h: high24h.toFixed(2),
          low24h: low24h.toFixed(2),
          volume: statsData.v,
          looksLikeGold: currentPrice > 1500 ? 'Yes (>$1500)' : 'No (<$1500)'
        });

        const goldData: GoldPrice = {
          price: Number(currentPrice.toFixed(2)), // Current price
          change: Number(priceChange.toFixed(2)), // Price change
          changePercent: Number(priceChangePercent.toFixed(2)), // Price change percent
          timestamp: Date.now(),
          high24h: Number(high24h.toFixed(2)), // 24h high
          low24h: Number(low24h.toFixed(2)), // 24h low
          volume: formatVolume(statsData.v || statsData.volume || '0') // 24h volume
        };

        console.log('💰 Final processed gold price data:', goldData);
        setGoldPrice(goldData);
      } else {
        // Fallback to latest historical data if stats fail
        const latestPrice = goldPrices[goldPrices.length - 1];
        const previousPrice = goldPrices[goldPrices.length - 2];
        const change = latestPrice - previousPrice;
        const changePercent = (change / previousPrice) * 100;

        setGoldPrice({
          price: Number(latestPrice.toFixed(2)),
          change: Number(change.toFixed(2)),
          changePercent: Number(changePercent.toFixed(2)),
          timestamp: Date.now(),
          high24h: Number(latestPrice.toFixed(2)),
          low24h: Number(latestPrice.toFixed(2)),
          volume: "0"
        });
      }

      setError(null); // Clear error on success
      setDataSource('Binance API (PAX Gold)');
      console.log('✅ Setting loading to false and updating state');
      setIsLoading(false);
      isLoadingRef.current = false;

    } catch (err) {
      console.error('❌ Binance API failed:', err);
      setError('Binance API unavailable - using simulated data');
      setDataSource('Simulated Data');
      generateSimulatedData();
      isLoadingRef.current = false;
    }
  };

  // Generate simulated data as fallback
  const generateSimulatedData = () => {
    const basePrice = 2045.50;
    const simulatedPoints: number[] = [];

    for (let i = 29; i >= 0; i--) {
      const variation = (Math.random() - 0.5) * 40;
      const price = basePrice + variation + (Math.sin(i / 5) * 15);
      simulatedPoints.push(Number(Math.max(price, 1800).toFixed(2)));
    }

    setPriceHistory(simulatedPoints);

    // Set current price to latest simulated data
    const latest = simulatedPoints[simulatedPoints.length - 1];
    const previous = simulatedPoints[simulatedPoints.length - 2];
    const change = latest - previous;
    const changePercent = (change / previous) * 100;

    setGoldPrice({
      price: latest,
      change: Number(change.toFixed(2)),
      changePercent: Number(changePercent.toFixed(2)),
      timestamp: Date.now(),
      high24h: Math.max(...simulatedPoints),
      low24h: Math.min(...simulatedPoints),
      volume: "0"
    });

    setIsLoading(false);
  };

  // Note: Real-time updates disabled - chart uses REST API data only

  // Track container width for responsive chart
  useEffect(() => {
    const updateContainerWidth = () => {
      if (chartContainerRef.current) {
        const width = chartContainerRef.current.offsetWidth;
        setContainerWidth(Math.max(width - 32, 300)); // Subtract padding, minimum 300px
      }
    };

    // Initial measurement
    updateContainerWidth();

    // Set up resize observer
    const resizeObserver = new ResizeObserver(updateContainerWidth);
    if (chartContainerRef.current) {
      resizeObserver.observe(chartContainerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Initialize data on component mount (static data only)
  useEffect(() => {
    console.log('🚀 Initializing gold price chart with static Binance data...');
    console.log('🔄 Starting one-time data fetch...');

    let dataFetched = false; // Track if data was successfully fetched

    // Initialize with historical data once
    const initializeData = async () => {
      try {
        await fetchHistoricalData();
        dataFetched = true; // Mark as successfully fetched
        console.log('✅ Static data fetch completed - no further updates');
      } catch (error) {
        console.error('❌ Data fetch failed:', error);
        console.log('🔄 Falling back to simulated data...');
        generateSimulatedData();
        dataFetched = true; // Mark as handled (even with simulated data)
      }
    };

    initializeData();

    // Fallback timeout only if data fetch hasn't completed
    const timeoutId = setTimeout(() => {
      if (!dataFetched) {
        console.log('⚠️ Data fetch timeout after 15 seconds - no response from API');
        console.log('📊 Current state:', {
          isLoading,
          error,
          priceHistoryLength: priceHistory.length,
          dataSource
        });
        console.log('🔄 Generating simulated data as fallback...');
        generateSimulatedData();
      } else {
        console.log('✅ Timeout check: Data already loaded successfully');
      }
    }, 15000); // Increased to 15 seconds

    // Note: Chart configured for static data only - no automatic updates
    console.log('📊 Chart configured for static data - no WebSocket or periodic updates');

    // Cleanup timeout on unmount
    return () => {
      clearTimeout(timeoutId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Intentionally empty - we only want this to run once on mount

  // Generate chart data with axes (memoized to prevent repeated calculations)
  const chartData = useMemo(() => {
    console.log('🎨 Generating chart data... (memoized)');

    if (priceHistory.length < 2) {
      console.log('⚠️ Not enough price data for chart, need at least 2 points');
      return { path: "", xLabels: [], yLabels: [], chartArea: null };
    }

    const width = containerWidth;
    const height = 200;
    const paddingLeft = 60;
    const paddingRight = 30;
    const paddingTop = 30;
    const paddingBottom = 40;
    const chartWidth = width - paddingLeft - paddingRight;
    const chartHeight = height - paddingTop - paddingBottom;

    const minPrice = Math.min(...priceHistory);
    const maxPrice = Math.max(...priceHistory);
    const priceRange = maxPrice - minPrice || 1;

    console.log('📊 Chart calculations:');
    console.log('  - Chart area:', chartWidth, 'x', chartHeight);
    console.log('  - Price range:', minPrice.toFixed(2), '-', maxPrice.toFixed(2));

    // Generate price line points
    const points = priceHistory.map((price, index) => {
      const x = paddingLeft + (index / (priceHistory.length - 1)) * chartWidth;
      const y = paddingTop + ((maxPrice - price) / priceRange) * chartHeight;
      return `${x},${y}`;
    });

    const path = `M ${points.join(' L ')}`;

    // Generate Y-axis labels (price levels)
    const yLabels = [];
    const numYLabels = 5;
    for (let i = 0; i < numYLabels; i++) {
      const price = minPrice + (priceRange * i) / (numYLabels - 1);
      const y = paddingTop + chartHeight - (i * chartHeight) / (numYLabels - 1);
      yLabels.push({
        value: price.toFixed(0),
        y: y,
        x: paddingLeft - 5
      });
    }

    // Generate X-axis labels (time periods)
    const xLabels = [];
    const numXLabels = 5;
    const daysAgo = priceHistory.length - 1;

    for (let i = 0; i < numXLabels; i++) {
      const dayIndex = Math.floor((i * (priceHistory.length - 1)) / (numXLabels - 1));
      const daysFromNow = daysAgo - dayIndex;
      const x = paddingLeft + (dayIndex / (priceHistory.length - 1)) * chartWidth;

      let label;
      if (daysFromNow === 0) {
        label = 'Now';
      } else if (daysFromNow === 1) {
        label = '1d';
      } else if (daysFromNow < 7) {
        label = `${daysFromNow}d`;
      } else {
        label = `${Math.floor(daysFromNow / 7)}w`;
      }

      xLabels.push({
        value: label,
        x: x,
        y: height - paddingBottom + 15
      });
    }

    const chartArea = {
      x: paddingLeft,
      y: paddingTop,
      width: chartWidth,
      height: chartHeight,
      paddingLeft,
      paddingRight,
      paddingTop,
      paddingBottom
    };

    return { path, xLabels, yLabels, chartArea };
  }, [priceHistory, containerWidth]); // Recalculate when priceHistory or containerWidth changes

  // Handle mouse movement over chart
  const handleMouseMove = (event: React.MouseEvent<SVGElement>) => {
    if (!chartData.chartArea || priceHistory.length < 2) return;

    const svgRect = event.currentTarget.getBoundingClientRect();
    const mouseX = event.clientX - svgRect.left;
    const mouseY = event.clientY - svgRect.top;

    // Convert mouse coordinates to SVG coordinates
    const svgX = (mouseX / svgRect.width) * containerWidth;
    const svgY = (mouseY / svgRect.height) * 200;

    // Check if mouse is within chart area
    if (
      svgX >= chartData.chartArea.x &&
      svgX <= chartData.chartArea.x + chartData.chartArea.width &&
      svgY >= chartData.chartArea.y &&
      svgY <= chartData.chartArea.y + chartData.chartArea.height
    ) {
      // Calculate which data point we're closest to
      const relativeX = svgX - chartData.chartArea.x;
      const dataIndex = Math.round((relativeX / chartData.chartArea.width) * (priceHistory.length - 1));
      const clampedIndex = Math.max(0, Math.min(dataIndex, priceHistory.length - 1));

      // Get the price at this index
      const price = priceHistory[clampedIndex];
      const daysAgo = priceHistory.length - 1 - clampedIndex;

      // Calculate exact position for this data point
      const exactX = chartData.chartArea.x + (clampedIndex / (priceHistory.length - 1)) * chartData.chartArea.width;
      const minPrice = Math.min(...priceHistory);
      const maxPrice = Math.max(...priceHistory);
      const priceRange = maxPrice - minPrice || 1;
      const exactY = chartData.chartArea.y + ((maxPrice - price) / priceRange) * chartData.chartArea.height;

      setHoverData({
        x: exactX,
        y: exactY,
        price: price,
        day: daysAgo
      });
    } else {
      setHoverData(null);
    }
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    setHoverData(null);
  };

  const isPositive = goldPrice ? goldPrice.change >= 0 : true;

  // Debug render state (memoized to prevent repeated logs)
  useMemo(() => {
    const state = {
      isLoading,
      error,
      priceHistoryLength: priceHistory.length,
      pathDataLength: chartData.path.length,
      goldPrice: goldPrice?.price || 'null',
      hasPathData: chartData.path.length > 0,
      xLabelsCount: chartData.xLabels.length,
      yLabelsCount: chartData.yLabels.length,
      dataSource
    };
    console.log('🖼️ Render state (memoized):', state);
    return state;
  }, [isLoading, error, priceHistory.length, chartData.path.length, goldPrice?.price, chartData.xLabels.length, chartData.yLabels.length, dataSource]);

  return (
    <Card className="w-full bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 flex items-center justify-center">
              <span className="text-black font-bold text-sm">AU</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">Gold</h3>
              <p className="text-xs text-gray-400">XAU/USD</p>
            </div>
          </div>
          <div className="text-right">
            {goldPrice ? (
              <>
                <div className="text-2xl font-bold text-white">
                  ${goldPrice.price.toLocaleString()}
                </div>
                <div className={`text-sm flex items-center gap-1 ${isPositive ? 'text-green-400' : 'text-red-400'
                  }`}>
                  <span>{isPositive ? '↗' : '↘'}</span>
                  <span>{isPositive ? '+' : ''}{goldPrice.change}</span>
                  <span>({isPositive ? '+' : ''}{goldPrice.changePercent.toFixed(2)}%)</span>
                </div>
              </>
            ) : (
              <>
                <div className="text-2xl font-bold text-white">
                  <div className="h-8 w-32 bg-gray-700 rounded animate-pulse"></div>
                </div>
                <div className="text-sm flex items-center gap-1 mt-1">
                  <div className="h-4 w-24 bg-gray-700 rounded animate-pulse"></div>
                </div>
              </>
            )}
          </div>
        </div>
      </CardHeader>

      <CardBody className="pt-0">
        <div className="space-y-4">
          {/* Price Chart */}
          <div ref={chartContainerRef} className="relative h-64 bg-gray-800/50 rounded-lg p-4 overflow-hidden">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mb-2"></div>
                <div className="text-xs text-gray-400">Loading gold price data...</div>
              </div>
            ) : priceHistory.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center p-4">
                <div className="text-red-400 text-sm mb-2">❌ No Data</div>
                <div className="text-gray-400 text-xs mb-2">No price history available</div>
                <div className="text-blue-400 text-xs">Click &quot;Reload Data&quot; to try again</div>
              </div>
            ) : error && !error.includes('Live data') ? (
              <div className="flex flex-col items-center justify-center h-full text-center p-4">
                <div className="text-yellow-400 text-sm mb-2">⚠️ API Notice</div>
                <div className="text-gray-400 text-xs mb-2">{error}</div>
                <div className="text-gray-500 text-xs mb-2">Using simulated data for demonstration</div>
                <div className="text-blue-400 text-xs">
                  Check API connectivity or try reloading
                </div>
              </div>
            ) : (
              <svg
                key={renderKey}
                width="100%"
                height="100%"
                viewBox={`0 0 ${containerWidth} 200`}
                className="overflow-visible cursor-crosshair"
                onMouseMove={handleMouseMove}
                onMouseLeave={handleMouseLeave}
              >
                <defs>
                  <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" stopColor="rgba(59, 130, 246, 0.3)" />
                    <stop offset="100%" stopColor="rgba(59, 130, 246, 0.05)" />
                  </linearGradient>
                </defs>

                {/* Chart Background Grid */}
                {chartData.chartArea && (
                  <>
                    {/* Horizontal grid lines */}
                    {chartData.yLabels.map((label, index) => (
                      <line
                        key={`hgrid-${index}`}
                        x1={chartData.chartArea.x}
                        y1={label.y}
                        x2={chartData.chartArea.x + chartData.chartArea.width}
                        y2={label.y}
                        stroke="rgba(156, 163, 175, 0.2)"
                        strokeWidth="1"
                      />
                    ))}

                    {/* Vertical grid lines */}
                    {chartData.xLabels.map((label, index) => (
                      <line
                        key={`vgrid-${index}`}
                        x1={label.x}
                        y1={chartData.chartArea.y}
                        x2={label.x}
                        y2={chartData.chartArea.y + chartData.chartArea.height}
                        stroke="rgba(156, 163, 175, 0.2)"
                        strokeWidth="1"
                      />
                    ))}
                  </>
                )}

                {chartData.path && chartData.chartArea && (
                  <>
                    {/* Fill area under curve */}
                    <motion.path
                      d={`${chartData.path} L ${chartData.chartArea.x + chartData.chartArea.width},${chartData.chartArea.y + chartData.chartArea.height} L ${chartData.chartArea.x},${chartData.chartArea.y + chartData.chartArea.height} Z`}
                      fill="url(#chartGradient)"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 1.5, delay: 0.5 }}
                    />

                    {/* Main price line */}
                    <motion.path
                      d={chartData.path}
                      stroke={isPositive ? "#10b981" : "#ef4444"}
                      strokeWidth="2"
                      fill="none"
                      initial={{ pathLength: 0 }}
                      animate={{ pathLength: 1 }}
                      transition={{ duration: 2, ease: "easeInOut" }}
                    />

                    {/* Animated current price dot */}
                    {priceHistory.length > 0 && chartData.chartArea && goldPrice && (() => {
                      const minPrice = Math.min(...priceHistory);
                      const maxPrice = Math.max(...priceHistory);
                      const priceRange = maxPrice - minPrice;

                      // Ensure we have valid price data and avoid division by zero
                      if (priceRange === 0 || isNaN(goldPrice.price) || isNaN(minPrice) || isNaN(maxPrice)) {
                        return null;
                      }

                      const cy = chartData.chartArea.y + ((maxPrice - goldPrice.price) / priceRange) * chartData.chartArea.height;

                      // Ensure cy is a valid number
                      if (isNaN(cy)) {
                        return null;
                      }

                      return (
                        <motion.circle
                          cx={chartData.chartArea.x + chartData.chartArea.width}
                          cy={cy}
                          r="4"
                          fill={isPositive ? "#10b981" : "#ef4444"}
                          initial={{ scale: 0 }}
                          animate={{ scale: [1, 1.3, 1] }}
                          transition={{ duration: 0.8, repeat: Infinity, repeatDelay: 2 }}
                          filter="drop-shadow(0 0 6px rgba(59, 130, 246, 0.5))"
                        />
                      );
                    })()}
                  </>
                )}

                {/* Y-axis labels (Price) */}
                {chartData.yLabels.map((label, index) => (
                  <text
                    key={`ylabel-${index}`}
                    x={label.x}
                    y={label.y + 4}
                    textAnchor="end"
                    fontSize="10"
                    fill="rgba(156, 163, 175, 0.8)"
                    fontFamily="monospace"
                  >
                    ${label.value}
                  </text>
                ))}

                {/* X-axis labels (Time) */}
                {chartData.xLabels.map((label, index) => (
                  <text
                    key={`xlabel-${index}`}
                    x={label.x}
                    y={label.y}
                    textAnchor="middle"
                    fontSize="10"
                    fill="rgba(156, 163, 175, 0.8)"
                    fontFamily="monospace"
                  >
                    {label.value}
                  </text>
                ))}

                {/* Axis lines */}
                {chartData.chartArea && (
                  <>
                    {/* Y-axis */}
                    <line
                      x1={chartData.chartArea.x}
                      y1={chartData.chartArea.y}
                      x2={chartData.chartArea.x}
                      y2={chartData.chartArea.y + chartData.chartArea.height}
                      stroke="rgba(156, 163, 175, 0.5)"
                      strokeWidth="1"
                    />

                    {/* X-axis */}
                    <line
                      x1={chartData.chartArea.x}
                      y1={chartData.chartArea.y + chartData.chartArea.height}
                      x2={chartData.chartArea.x + chartData.chartArea.width}
                      y2={chartData.chartArea.y + chartData.chartArea.height}
                      stroke="rgba(156, 163, 175, 0.5)"
                      strokeWidth="1"
                    />
                  </>
                )}

                {/* Hover crosshair and tooltip */}
                {hoverData && chartData.chartArea && (
                  <>
                    {/* Vertical crosshair line */}
                    <line
                      x1={hoverData.x}
                      y1={chartData.chartArea.y}
                      x2={hoverData.x}
                      y2={chartData.chartArea.y + chartData.chartArea.height}
                      stroke="rgba(59, 130, 246, 0.8)"
                      strokeWidth="1"
                      strokeDasharray="4,4"
                    />

                    {/* Horizontal crosshair line */}
                    <line
                      x1={chartData.chartArea.x}
                      y1={hoverData.y}
                      x2={chartData.chartArea.x + chartData.chartArea.width}
                      y2={hoverData.y}
                      stroke="rgba(59, 130, 246, 0.8)"
                      strokeWidth="1"
                      strokeDasharray="4,4"
                    />

                    {/* Hover point */}
                    <circle
                      cx={hoverData.x}
                      cy={hoverData.y}
                      r="4"
                      fill="rgba(59, 130, 246, 1)"
                      stroke="white"
                      strokeWidth="2"
                    />

                    {/* Tooltip background */}
                    <rect
                      x={hoverData.x > containerWidth / 2 ? hoverData.x - 120 : hoverData.x + 10}
                      y={hoverData.y - 35}
                      width="110"
                      height="30"
                      fill="rgba(0, 0, 0, 0.9)"
                      stroke="rgba(59, 130, 246, 0.8)"
                      strokeWidth="1"
                      rx="4"
                    />

                    {/* Tooltip text */}
                    <text
                      x={hoverData.x > containerWidth / 2 ? hoverData.x - 65 : hoverData.x + 65}
                      y={hoverData.y - 20}
                      textAnchor="middle"
                      fontSize="12"
                      fill="white"
                      fontFamily="monospace"
                    >
                      ${hoverData.price.toFixed(2)}
                    </text>
                    <text
                      x={hoverData.x > containerWidth / 2 ? hoverData.x - 65 : hoverData.x + 65}
                      y={hoverData.y - 8}
                      textAnchor="middle"
                      fontSize="10"
                      fill="rgba(156, 163, 175, 1)"
                      fontFamily="monospace"
                    >
                      {hoverData.day === 0 ? 'Now' : `${hoverData.day}d ago`}
                    </text>
                  </>
                )}
              </svg>
            )}
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-gray-800/30 rounded-lg p-3">
              <div className="text-xs text-gray-400 mb-1">24h High</div>
              <div className="text-sm font-semibold text-green-400">
                {goldPrice ? (
                  `$${goldPrice.high24h.toLocaleString()}`
                ) : (
                  <div className="h-4 w-16 bg-gray-700 rounded animate-pulse mx-auto"></div>
                )}
              </div>
            </div>
            <div className="bg-gray-800/30 rounded-lg p-3">
              <div className="text-xs text-gray-400 mb-1">24h Low</div>
              <div className="text-sm font-semibold text-red-400">
                {goldPrice ? (
                  `$${goldPrice.low24h.toLocaleString()}`
                ) : (
                  <div className="h-4 w-16 bg-gray-700 rounded animate-pulse mx-auto"></div>
                )}
              </div>
            </div>
            <div className="bg-gray-800/30 rounded-lg p-3">
              <div className="text-xs text-gray-400 mb-1">Volume</div>
              <div className="text-sm font-semibold text-blue-400">
                {goldPrice ? (
                  goldPrice.volume
                ) : (
                  <div className="h-4 w-12 bg-gray-700 rounded animate-pulse mx-auto"></div>
                )}
              </div>
            </div>
          </div>

          {/* Market Status */}
          <div className="flex items-center justify-between text-xs text-gray-400 mt-4">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full animate-pulse ${dataSource.includes('Binance') ? 'bg-blue-400' :
                'bg-yellow-400'
                }`}></div>
              <span className="text-xs">
                {dataSource.includes('Binance') ? dataSource :
                  error ? 'API Error - Simulated Data' : 'Loading...'}
              </span>
            </div>
            <span>Updated: {goldPrice ? new Date(goldPrice.timestamp).toLocaleTimeString() : '--:--:--'}</span>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
