'use client'

import { Card, CardBody } from "@heroui/react";
import { motion } from "framer-motion";
import { useRouter, usePathname } from "next/navigation";



interface FloatingNavigationProps {
  activeSection: string;
  showFloatingNav: boolean;
  scrollToSection: (sectionId: string) => void;
}

export default function FloatingNavigation({
  activeSection,
  showFloatingNav,
  scrollToSection
}: FloatingNavigationProps) {
  const router = useRouter();
  const pathname = usePathname();

  const sections = [
    { id: '/', title: 'หน้าหลัก', icon: '●' },
    { id: '/#learn', title: 'เรียนรู้', icon: '●' },
    { id: '/#brokers', title: 'โบรกเกอร์', icon: '●' },
    { id: '/#systems', title: 'ระบบเทรด', icon: '●' },
    { id: '/blogs', title: 'บทความ', icon: '●' },
    { id: '/#contact', title: 'ติดต่อ', icon: '●' }
  ];

  const handleScrollToSection = (sectionId: string) => {
    // Handle special case for home page navigation
    if (sectionId === '/') {
      if (pathname === '/') {
        // Already on home page, scroll to hero
        scrollToSection('hero');
      } else {
        // Navigate to home page
        router.push('/');
      }
      return;
    }

    // Handle direct page routes (like /blogs)
    if (sectionId.startsWith('/') && !sectionId.startsWith('/#')) {
      router.push(sectionId);
      return;
    }

    // Handle hash-based navigation (e.g., /#hero, /#learn)
    if (sectionId.startsWith('/#')) {
      const targetSection = sectionId.substring(2); // Remove '/#' prefix

      if (pathname === '/') {
        // Already on home page, scroll to the target section
        scrollToSection(targetSection);
      } else {
        // Navigate to home page with hash
        router.push(sectionId);
      }
      return;
    }

    // Handle regular section IDs
    if (pathname === '/') {
      // On home page, scroll to section
      scrollToSection(sectionId);
    } else {
      // On other pages, navigate to home with hash
      router.push(`/#${sectionId}`);
    }
  };

  // Helper function to check if a section is active
  const isSectionActive = (sectionId: string) => {
    if (sectionId === '/') {
      return pathname === '/' && activeSection === 'hero';
    }
    if (sectionId.startsWith('/') && !sectionId.startsWith('/#')) {
      return pathname === sectionId;
    }
    if (sectionId.startsWith('/#')) {
      return pathname === '/' && activeSection === sectionId.substring(2);
    }
    return pathname === '/' && activeSection === sectionId;
  };
  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{
        opacity: showFloatingNav ? 1 : 0,
        x: showFloatingNav ? 0 : 50
      }}
      transition={{ duration: 0.5 }}
      className="fixed right-6 top-1/2 transform -translate-y-1/2 z-40 pointer-events-none hidden lg:block"
      style={{ pointerEvents: showFloatingNav ? 'auto' : 'none' }}
    >
      <Card className="bg-black/40 backdrop-blur-xl border border-blue-500/20 p-2 min-w-[200px]">
        <CardBody className="p-3">
          <h4 className="text-white font-semibold text-sm mb-3 text-center">สารบัญ</h4>
          <div className="space-y-2">
            {sections.map((section, index) => (
              <motion.button
                key={section.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                whileHover={{ x: 5, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`w-full text-left p-2 rounded-lg transition-all duration-300 flex items-center gap-2 text-xs ${isSectionActive(section.id)
                  ? 'bg-blue-500/30 text-blue-300 border border-blue-500/50 shadow-lg shadow-blue-500/25'
                  : 'text-white/70 hover:text-white hover:bg-blue-500/10 border border-transparent'
                  }`}
                onClick={() => handleScrollToSection(section.id)}
              >
                <span className="text-sm">{section.icon}</span>
                <span className="font-medium">{section.title}</span>
                {isSectionActive(section.id) && (
                  <motion.div
                    className="ml-auto w-1.5 h-1.5 bg-blue-400 rounded-full"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                )}
              </motion.button>
            ))}
          </div>

          {/* Progress Indicator */}
          <div className="mt-4 pt-3 border-t border-gray-700">
            <div className="flex justify-between text-xs text-gray-400 mb-1">
              <span>ความคืบหน้า</span>
              <span>{Math.round(((sections.findIndex(s => s.id === activeSection) + 1) / sections.length) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-800 rounded-full h-1">
              <motion.div
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-1 rounded-full"
                initial={{ width: 0 }}
                animate={{
                  width: `${((sections.findIndex(s => s.id === activeSection) + 1) / sections.length) * 100}%`
                }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
}
