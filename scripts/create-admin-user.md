# Create Admin User in Supabase

## Method 1: Using Supabase Dashboard (Recommended)

1. **Go to your Supabase project dashboard**
   - Visit https://supabase.com/dashboard
   - Select your project

2. **Navigate to Authentication**
   - Click on "Authentication" in the left sidebar
   - Click on "Users" tab

3. **Create a new user**
   - Click "Add user" button
   - Fill in the form:
     - **Email**: `<EMAIL>` (or your preferred admin email)
     - **Password**: `admin123` (or your preferred secure password)
     - **Auto Confirm User**: ✅ (check this box)
   - Click "Create user"

4. **Set admin role via Raw User Meta Data**
   - After creating the user, click on the user email in the users list
   - Look for **Raw User Meta Data** section (this corresponds to `user_metadata` in code)
   - Click the **Edit** button next to it
   - Replace the content with:
     ```json
     {
       "role": "admin"
     }
     ```
   - Click "Save"

**Note**: The Supabase dashboard interface has been updated. The old "User Metadata" and "App Metadata" sections have been replaced with "Raw User Meta Data" and "Raw App Meta Data".

## Method 2: Using SQL Editor (Alternative)

If you can't find the Raw User Meta Data section or prefer SQL, you can use the SQL Editor:

### Option A: Update existing user to be admin
```sql
-- Update existing user to be admin (replace email with your admin email)
UPDATE auth.users
SET raw_user_meta_data = '{"role": "admin"}'::jsonb
WHERE email = '<EMAIL>';
```

### Option B: Create new admin user entirely with SQL
```sql
-- Create new admin user with proper structure
INSERT INTO auth.users (
  instance_id,
  id,
  aud,
  role,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
) VALUES (
  '00000000-0000-0000-0000-000000000000',
  gen_random_uuid(),
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('admin123', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"role": "admin"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
);
```

**⚠️ Note**: The SQL method is more complex. Use the Dashboard method (Method 1) when possible.

## Method 3: Verification Query

To verify your admin user was created correctly, run this query in the SQL Editor:

```sql
-- Check if admin user exists with correct metadata
SELECT email, raw_user_meta_data, raw_app_meta_data
FROM auth.users
WHERE email = '<EMAIL>';
```

You should see the user with `raw_user_meta_data` containing `{"role": "admin"}`.

## Testing the Admin Access

1. **Go to the admin login page**
   - Visit: `http://localhost:3000/admin/login`

2. **Login with your admin credentials**
   - Email: `<EMAIL>`
   - Password: `admin123`

3. **Verify access**
   - You should be redirected to `/admin` dashboard
   - You should see the admin panel with all features

4. **Test image upload permissions**
   - Go to `/admin/blogs/new`
   - Try uploading an image in the featured image section
   - Should work successfully with Supabase Storage URL

## Important Notes About Current Interface

- **Raw User Meta Data** = `user_metadata` in JavaScript code
- **Raw App Meta Data** = `app_metadata` in JavaScript code
- The old "User Metadata" and "App Metadata" sections no longer exist in the current Supabase dashboard
- Always use JSON format when editing metadata: `{"role": "admin"}`

## Troubleshooting

### "ไม่มีสิทธิ์เข้าถึง" (No Access)
- Check that the user has `"role": "admin"` in Raw User Meta Data or Raw App Meta Data
- Verify the JSON format is correct: `{"role": "admin"}`
- Check the browser console for any errors
- Run the verification query to confirm the metadata was saved

### "Invalid login credentials"
- Verify the email and password are correct
- Make sure "Auto Confirm User" was checked when creating the user
- Check if the user exists in Authentication > Users

### Database Connection Issues
- Verify your Supabase environment variables in `.env.local`
- Check that your Supabase project is active
- Ensure RLS policies allow the operations

## Security Notes

### For Production:
1. **Change default credentials** - Don't use `admin123` in production
2. **Use strong passwords** - Generate secure passwords
3. **Limit admin users** - Only create necessary admin accounts
4. **Enable MFA** - Consider multi-factor authentication
5. **Regular audits** - Review admin access regularly

### Current Admin Logic:
The system considers a user as admin if:
- `user.user_metadata.role === "admin"`, OR
- `user.app_metadata.role === "admin"`

This corresponds to:
- **Raw User Meta Data** containing `{"role": "admin"}`, OR
- **Raw App Meta Data** containing `{"role": "admin"}`

You can modify this logic in `src/contexts/AuthContext.tsx` if needed.

## Next Steps

After creating your admin user:
1. Login to the admin panel
2. Create your first blog post
3. Test all admin features
4. Set up proper user roles for production
5. Configure email templates in Supabase (optional)

## Support

If you encounter issues:
1. Check the browser console for errors
2. Verify Supabase project settings
3. Review the authentication flow in the code
4. Check Supabase logs in the dashboard
