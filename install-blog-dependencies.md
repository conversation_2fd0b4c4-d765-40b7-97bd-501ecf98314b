# Blog System Dependencies Installation

Run the following command to install the required dependencies for the blog system:

```bash
npm install @supabase/supabase-js
```

## Additional Setup Steps

1. **Create Supabase Project:**
   - Go to https://supabase.com
   - Create a new project
   - Get your project URL and anon key from Settings > API

2. **Set Environment Variables:**
   - Copy `.env.example` to `.env.local`
   - Fill in your Supabase credentials:
     ```
     NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
     NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
     ```

3. **Run Database Migration:**
   - In your Supabase dashboard, go to SQL Editor
   - Copy and paste the content from `supabase/migrations/001_create_blogs_table.sql`
   - Run the migration to create the blogs table and sample data

4. **Upload Sample Images (Optional):**
   - Create a storage bucket named "blog-images" in Supabase Storage
   - Upload sample images for the blog posts
   - Update the image URLs in the sample data

## Features Included

### Blog List Page (/blogs)
- ✅ Responsive grid layout
- ✅ Search functionality
- ✅ Tag filtering
- ✅ Featured blogs section
- ✅ Pagination with "Load More"
- ✅ SEO-friendly URLs

### Blog Detail Page (/blogs/[slug])
- ✅ Rich content rendering (Medium-like)
- ✅ Social sharing buttons
- ✅ Related articles
- ✅ View count tracking
- ✅ Reading time estimation
- ✅ Breadcrumb navigation

### Content Management
- ✅ Rich text content stored as JSON
- ✅ Support for headings, paragraphs, images, quotes, lists, code blocks
- ✅ Text formatting (bold, italic, underline, strikethrough, links)
- ✅ Image support with captions
- ✅ SEO meta tags
- ✅ Auto-generated slugs
- ✅ Tag system

### Database Features
- ✅ PostgreSQL with Supabase
- ✅ Row Level Security (RLS)
- ✅ Auto-generated timestamps
- ✅ Full-text search capabilities
- ✅ Optimized indexes for performance

## Content Structure

The blog content is stored as JSON in a format similar to Medium's editor:

```json
[
  {
    "type": "paragraph",
    "content": [
      {
        "type": "text",
        "text": "Your paragraph text here",
        "marks": [
          {
            "type": "bold"
          }
        ]
      }
    ]
  },
  {
    "type": "heading",
    "attrs": { "level": 2 },
    "content": [
      {
        "type": "text",
        "text": "Your heading text"
      }
    ]
  },
  {
    "type": "image",
    "attrs": {
      "src": "/path/to/image.jpg",
      "alt": "Image description",
      "caption": "Image caption"
    }
  }
]
```

This structure allows for flexible content creation similar to modern editors like Medium, Notion, or WordPress Gutenberg blocks.
