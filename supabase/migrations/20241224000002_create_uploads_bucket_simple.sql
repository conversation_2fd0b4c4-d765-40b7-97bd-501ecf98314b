-- Simple Migration: Create uploads bucket with RLS policies
-- Created: 2024-12-24
-- Description: Simplified version that works with Supabase Dashboard SQL Editor

-- Step 1: Create the uploads bucket (if it doesn't exist)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'uploads') THEN
    INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
    VALUES (
      'uploads',
      'uploads',
      true,
      5242880, -- 5MB limit
      ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
    );
  END IF;
END $$;

-- Step 2: Create RLS policies for the uploads bucket

-- Policy 1: Allow public read access to all files in uploads bucket
CREATE POLICY IF NOT EXISTS "Public read access for uploads bucket"
ON storage.objects
FOR SELECT
USING (bucket_id = 'uploads');

-- Policy 2: Allow authenticated admin users to upload files
CREATE POLICY IF NOT EXISTS "Admin upload access for uploads bucket"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'uploads' 
  AND auth.role() = 'authenticated'
  AND (
    -- Check if user email contains 'admin'
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    -- Check if user has admin role in user_metadata
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    -- Check if user has admin role in app_metadata
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
);

-- Policy 3: Allow admin users to update their own uploaded files
CREATE POLICY IF NOT EXISTS "Admin update access for uploads bucket"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'uploads'
  AND auth.role() = 'authenticated'
  AND (
    -- Check if user email contains 'admin'
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    -- Check if user has admin role in user_metadata
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    -- Check if user has admin role in app_metadata
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
  AND owner = auth.uid()
)
WITH CHECK (
  bucket_id = 'uploads'
  AND auth.role() = 'authenticated'
  AND (
    -- Check if user email contains 'admin'
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    -- Check if user has admin role in user_metadata
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    -- Check if user has admin role in app_metadata
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
);

-- Policy 4: Allow admin users to delete their own uploaded files
CREATE POLICY IF NOT EXISTS "Admin delete access for uploads bucket"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'uploads'
  AND auth.role() = 'authenticated'
  AND (
    -- Check if user email contains 'admin'
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    -- Check if user has admin role in user_metadata
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    -- Check if user has admin role in app_metadata
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
  AND owner = auth.uid()
);

-- Verification: Check if everything was created successfully
DO $$
BEGIN
  -- Check if bucket exists
  IF EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'uploads') THEN
    RAISE NOTICE 'SUCCESS: uploads bucket created successfully';
  ELSE
    RAISE NOTICE 'ERROR: uploads bucket was not created';
  END IF;
  
  -- Check if policies exist
  IF EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname LIKE '%uploads bucket%') THEN
    RAISE NOTICE 'SUCCESS: RLS policies created successfully';
  ELSE
    RAISE NOTICE 'ERROR: RLS policies were not created';
  END IF;
END $$;
