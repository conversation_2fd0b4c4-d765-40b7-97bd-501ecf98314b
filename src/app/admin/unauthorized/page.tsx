'use client'

import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Card, CardBody, Button } from '@heroui/react'
import { useAuth } from '@/contexts/AuthContext'

export default function UnauthorizedPage() {
  const router = useRouter()
  const { user, signOut } = useAuth()

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-lg"
      >
        <Card className="bg-black/20 border border-white/10 backdrop-blur-sm">
          <CardBody className="text-center p-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-8xl mb-6"
            >
              🚫
            </motion.div>

            <h1 className="text-3xl font-bold text-white mb-4">
              ไม่มีสิทธิ์เข้าถึง
            </h1>

            <p className="text-gray-400 mb-2">
              คุณไม่มีสิทธิ์เข้าถึงหน้าผู้ดูแลระบบ
            </p>

            {user && (
              <p className="text-gray-500 text-sm mb-6">
                เข้าสู่ระบบด้วย: {user.email}
              </p>
            )}

            <div className="space-y-4">
              <Button
                onPress={() => signOut().then(() => router.push('/admin/login'))}
                className="w-full bg-red-500 text-white hover:bg-red-600 font-semibold"
              >
                ออกจากระบบและเข้าสู่ระบบใหม่
              </Button>

              <Button
                variant="bordered"
                className="w-full border-white/20 text-white hover:bg-white/10"
                onPress={() => router.push('/')}
              >
                กลับไปเว็บไซต์หลัก
              </Button>
            </div>

            <div className="mt-8 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
              <h3 className="text-yellow-400 font-semibold mb-2 text-sm">
                💡 วิธีการได้รับสิทธิ์ผู้ดูแลระบบ:
              </h3>
              <div className="text-xs text-gray-400 space-y-1 text-left">
                <p>1. ติดต่อผู้ดูแลระบบเพื่อขอสิทธิ์</p>
                <p>{`2. หรือใช้บัญชีที่มีอีเมลที่มีคำว่า "admin"`}</p>
                <p>{`3. หรือตั้งค่า role เป็น "admin" ใน user metadata`}</p>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <h3 className="text-blue-400 font-semibold mb-2 text-sm">
                🔧 สำหรับนักพัฒนา:
              </h3>
              <div className="text-xs text-gray-400 space-y-1 text-left">
                <p>• ตรวจสอบ user metadata ใน Supabase Auth</p>
                <p>• เพิ่ม <code className="bg-gray-800 px-1 rounded">{`{"role": "admin"}`}</code></p>
                <p>• หรือแก้ไขเงื่อนไข isAdmin ใน AuthContext</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  )
}
