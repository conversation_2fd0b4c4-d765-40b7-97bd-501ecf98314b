'use client'

import { <PERSON><PERSON>, <PERSON>, CardBody, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Divider } from "@heroui/react";
import { motion } from "framer-motion";

export default function BrokersSection() {
  const brokers = [
    {
      name: "Exness",
      rating: 4.8,
      minDeposit: "$1",
      spread: "0.3 pips",
      leverage: "1:2000",
      features: ["ถอนเงินทันที", "ไม่มีค่าสวอป", "เครื่องมือมืออาชีพ"],
      badge: "ยอดนิยมสูงสุด",
      badgeColor: "bg-purple-500"
    },
    {
      name: "XM",
      rating: 4.7,
      minDeposit: "$5",
      spread: "0.6 pips",
      leverage: "1:888",
      features: ["โบนัส $30 ไม่ต้องฝาก", "แหล่งเรียนรู้", "สนับสนุน 24/7"],
      badge: "ดีสำหรับมือใหม่",
      badgeColor: "bg-blue-500"
    },
    {
      name: "IC Markets",
      rating: 4.9,
      minDeposit: "$200",
      spread: "0.0 pips",
      leverage: "1:500",
      features: ["สเปรดดิบ", "การดำเนินการรวดเร็ว", "แพลตฟอร์มมืออาชีพ"],
      badge: "สเปรดดีที่สุด",
      badgeColor: "bg-purple-500"
    }
  ];

  return (
    <div id="brokers" className="relative z-10 bg-black/20 backdrop-blur-sm">
      <div className="container mx-auto px-6 py-20">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            โบรกเกอร์ FOREX ยอดนิยม 2025
          </h2>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            โบรกเกอร์ที่ผ่านการรีวิวและทดสอบอย่างละเอียด ไม่มีเนื้อหาสปอนเซอร์ - เพียงรีวิวที่ซื่อสัตย์
          </p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8">
          {brokers.map((broker, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 + index * 0.1 }}
            >
              <Card className="bg-black/40 backdrop-blur-md border border-blue-500/20 hover:border-blue-500/50 transition-all duration-300 h-full">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between w-full mb-4">
                    <h3 className="text-xl font-bold text-white">{broker.name}</h3>
                    <Chip className={`${broker.badgeColor} text-white text-xs`}>
                      {broker.badge}
                    </Chip>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <span key={i} className={`text-sm ${i < Math.floor(broker.rating) ? 'text-yellow-400' : 'text-gray-600'}`}>
                          ★
                        </span>
                      ))}
                    </div>
                    <span className="text-gray-400 text-sm">{broker.rating}</span>
                  </div>
                </CardHeader>
                <CardBody className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-400">ฝากขั้นต่ำ</p>
                      <p className="text-white font-semibold">{broker.minDeposit}</p>
                    </div>
                    <div>
                      <p className="text-gray-400">สเปรดเริ่มต้น</p>
                      <p className="text-white font-semibold">{broker.spread}</p>
                    </div>
                    <div>
                      <p className="text-gray-400">เลเวอเรจสูงสุด</p>
                      <p className="text-white font-semibold">{broker.leverage}</p>
                    </div>
                  </div>

                  <Divider className="bg-gray-700" />

                  <div className="space-y-2">
                    {broker.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-purple-400 rounded-full"></div>
                        <p className="text-gray-300 text-sm">{feature}</p>
                      </div>
                    ))}
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button
                      className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold"
                      size="sm"
                    >
                      เปิดบัญชี
                    </Button>
                    <Button
                      variant="bordered"
                      className="border-purple-500/50 text-purple-300 hover:bg-purple-500/20"
                      size="sm"
                    >
                      รีวิว
                    </Button>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
