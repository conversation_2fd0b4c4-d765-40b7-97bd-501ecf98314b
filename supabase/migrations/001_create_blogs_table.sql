-- <PERSON>reate blogs table
CREATE TABLE IF NOT EXISTS blogs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  excerpt TEXT,
  content JSONB NOT NULL, -- Rich content stored as <PERSON><PERSON><PERSON> (similar to Medium's editor)
  featured_image TEXT, -- URL to featured image
  author_name VARCHAR(100) NOT NULL DEFAULT 'TraderBase Team',
  author_avatar TEXT,
  published BOOLEAN DEFAULT false,
  featured BOOLEAN DEFAULT false,
  view_count INTEGER DEFAULT 0,
  reading_time INTEGER, -- Estimated reading time in minutes
  tags TEXT[] DEFAULT '{}', -- Array of tags
  meta_title VARCHAR(255), -- SEO meta title
  meta_description TEXT, -- SEO meta description
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  published_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blogs_slug ON blogs(slug);
CREATE INDEX IF NOT EXISTS idx_blogs_published ON blogs(published);
CREATE INDEX IF NOT EXISTS idx_blogs_featured ON blogs(featured);
CREATE INDEX IF NOT EXISTS idx_blogs_published_at ON blogs(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_blogs_tags ON blogs USING GIN(tags);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_blogs_updated_at 
    BEFORE UPDATE ON blogs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to auto-generate slug from title
CREATE OR REPLACE FUNCTION generate_slug(title TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN lower(
        regexp_replace(
            regexp_replace(
                regexp_replace(title, '[^\w\s-]', '', 'g'),
                '\s+', '-', 'g'
            ),
            '-+', '-', 'g'
        )
    );
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate slug if not provided
CREATE OR REPLACE FUNCTION set_blog_slug()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.slug IS NULL OR NEW.slug = '' THEN
        NEW.slug = generate_slug(NEW.title);
        
        -- Ensure slug is unique
        WHILE EXISTS (SELECT 1 FROM blogs WHERE slug = NEW.slug AND id != COALESCE(NEW.id, gen_random_uuid())) LOOP
            NEW.slug = NEW.slug || '-' || extract(epoch from now())::int;
        END LOOP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_blog_slug_trigger
    BEFORE INSERT OR UPDATE ON blogs
    FOR EACH ROW
    EXECUTE FUNCTION set_blog_slug();

-- Insert sample blog data
INSERT INTO blogs (title, excerpt, content, featured_image, published, featured, tags, reading_time) VALUES
(
    'เริ่มต้นเทรด Forex สำหรับมือใหม่',
    'คู่มือสำหรับผู้เริ่มต้นที่ต้องการเรียนรู้การเทรด Forex อย่างถูกต้องและปลอดภัย',
    '[
        {
            "type": "paragraph",
            "content": [
                {
                    "type": "text",
                    "text": "การเทรด Forex เป็นหนึ่งในการลงทุนที่ได้รับความนิยมมากที่สุดในปัจจุบัน แต่สำหรับมือใหม่อาจรู้สึกว่าเป็นเรื่องที่ซับซ้อนและยากเข้าใจ"
                }
            ]
        },
        {
            "type": "heading",
            "attrs": { "level": 2 },
            "content": [
                {
                    "type": "text",
                    "text": "Forex คืออะไร?"
                }
            ]
        },
        {
            "type": "paragraph",
            "content": [
                {
                    "type": "text",
                    "text": "Forex หรือ Foreign Exchange คือตลาดการซื้อขายอัตราแลกเปลี่ยนเงินตราต่างประเทศ เป็นตลาดที่ใหญ่ที่สุดในโลกด้วยมูลค่าการซื้อขายกว่า 6 ล้านล้านดอลลาร์ต่อวัน"
                }
            ]
        }
    ]',
    '/images/blog/forex-beginner-guide.jpg',
    true,
    true,
    ARRAY['forex', 'beginner', 'trading', 'guide'],
    5
),
(
    'วิธีเลือกโบรกเกอร์ Forex ที่ดี',
    'เทคนิคและหลักเกณฑ์ในการเลือกโบรกเกอร์ Forex ที่เหมาะสมและน่าเชื่อถือ',
    '[
        {
            "type": "paragraph",
            "content": [
                {
                    "type": "text",
                    "text": "การเลือกโบรกเกอร์ Forex ที่ดีเป็นขั้นตอนสำคัญที่จะส่งผลต่อความสำเร็จในการเทรดของคุณ"
                }
            ]
        }
    ]',
    '/images/blog/choosing-forex-broker.jpg',
    true,
    false,
    ARRAY['forex', 'broker', 'selection', 'tips'],
    7
);

-- Enable Row Level Security (RLS)
ALTER TABLE blogs ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access to published blogs
CREATE POLICY "Public can read published blogs" ON blogs
    FOR SELECT USING (published = true);

-- Create secure RLS policies for blogs table

-- Allow everyone to read published blogs
CREATE POLICY "Anyone can read published blogs" ON blogs
    FOR SELECT USING (published = true);

-- Allow authenticated admin users to read all blogs (including drafts)
CREATE POLICY "Admin can read all blogs" ON blogs
    FOR SELECT USING (
        auth.role() = 'authenticated' AND (
            (auth.jwt() ->> 'user_metadata')::json ->> 'role' = 'admin' OR
            (auth.jwt() ->> 'app_metadata')::json ->> 'role' = 'admin'
        )
    );

-- Allow only admin users to insert blogs
CREATE POLICY "Admin can insert blogs" ON blogs
    FOR INSERT WITH CHECK (
        auth.role() = 'authenticated' AND (
            (auth.jwt() ->> 'user_metadata')::json ->> 'role' = 'admin' OR
            (auth.jwt() ->> 'app_metadata')::json ->> 'role' = 'admin'
        )
    );

-- Allow only admin users to update blogs
CREATE POLICY "Admin can update blogs" ON blogs
    FOR UPDATE USING (
        auth.role() = 'authenticated' AND (
            (auth.jwt() ->> 'user_metadata')::json ->> 'role' = 'admin' OR
            (auth.jwt() ->> 'app_metadata')::json ->> 'role' = 'admin'
        )
    );

-- Allow only admin users to delete blogs
CREATE POLICY "Admin can delete blogs" ON blogs
    FOR DELETE USING (
        auth.role() = 'authenticated' AND (
            (auth.jwt() ->> 'user_metadata')::json ->> 'role' = 'admin' OR
            (auth.jwt() ->> 'app_metadata')::json ->> 'role' = 'admin'
        )
    );
