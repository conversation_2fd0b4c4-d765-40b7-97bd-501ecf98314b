'use client'

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Card, CardBody, Button, Input, Chip } from "@heroui/react";
import { blogApi, Blog } from "@/lib/supabase";
import Link from "next/link";
import Image from "next/image";
import NavigationBar from "@/components/NavigationBar";
import FooterSection from "@/components/FooterSection";

export default function BlogsPage() {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [featuredBlogs, setFeaturedBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [tags, setTags] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [activeSection] = useState('blogs');
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const loadInitialData = useCallback(async () => {
    try {
      setLoading(true);
      const [blogsData, featuredData, tagsData] = await Promise.all([
        blogApi.getBlogs(1, 9),
        blogApi.getFeaturedBlogs(3),
        blogApi.getAllTags()
      ]);

      setBlogs(blogsData.blogs);
      setHasMore(blogsData.hasMore);
      setFeaturedBlogs(featuredData);
      setTags(tagsData);
    } catch (error) {
      console.error('Error loading blogs:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadBlogs = useCallback(async () => {
    try {
      const data = await blogApi.getBlogs(page, 9);
      if (page === 1) {
        setBlogs(data.blogs);
      } else {
        setBlogs(prev => [...prev, ...data.blogs]);
      }
      setHasMore(data.hasMore);
    } catch (error) {
      console.error('Error loading blogs:', error);
    }
  }, [page]);

  const handleSearch = useCallback(async () => {
    try {
      setLoading(true);
      let data;

      if (selectedTag) {
        data = await blogApi.getBlogsByTag(selectedTag, page, 9);
      } else if (searchQuery) {
        data = await blogApi.searchBlogs(searchQuery, page, 9);
      } else {
        data = await blogApi.getBlogs(page, 9);
      }

      if (page === 1) {
        setBlogs(data.blogs);
      } else {
        setBlogs(prev => [...prev, ...data.blogs]);
      }
      setHasMore(data.hasMore);
    } catch (error) {
      console.error('Error searching blogs:', error);
    } finally {
      setLoading(false);
    }
  }, [selectedTag, searchQuery, page]);

  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  useEffect(() => {
    if (searchQuery || selectedTag) {
      handleSearch();
    } else {
      loadBlogs();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, selectedTag, page]);

  const loadMore = () => {
    setPage(prev => prev + 1);
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedTag(null);
    setPage(1);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('th-TH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-blue-900/20 to-black overflow-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden -z-10">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Navigation */}
      <NavigationBar
        activeSection={activeSection}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
        scrollToSection={scrollToSection}
      />

      {/* Hero Section */}
      <section className="relative pt-32 pb-16 px-4 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute top-40 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-1/2 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-6xl mx-auto text-center">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-6xl font-bold text-white mb-6"
          >
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              TraderBase
            </span>{' '}
            <span className="text-white">Blog</span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto"
          >
            ความรู้และเทคนิคการเทรด Forex จากผู้เชี่ยวชาญ
          </motion.p>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="max-w-2xl mx-auto mb-8"
          >
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <Input
                placeholder="ค้นหาบทความ..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1"
                classNames={{
                  input: "bg-gray-900/50 text-white placeholder:text-gray-400",
                  inputWrapper: "bg-gray-900/50 border border-gray-700/50 hover:border-blue-500/50 backdrop-blur-sm"
                }}
              />
              {(searchQuery || selectedTag) && (
                <Button
                  onPress={clearFilters}
                  variant="bordered"
                  className="border-gray-700/50 text-gray-300 hover:bg-gray-800/50 hover:border-blue-500/50 backdrop-blur-sm"
                >
                  ล้างตัวกรอง
                </Button>
              )}
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 justify-center">
              {tags.slice(0, 8).map((tag) => (
                <Chip
                  key={tag}
                  onClick={() => setSelectedTag(selectedTag === tag ? null : tag)}
                  className={`cursor-pointer transition-all backdrop-blur-sm ${selectedTag === tag
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg shadow-blue-500/25'
                    : 'bg-gray-900/30 text-gray-300 hover:bg-gray-800/50 border border-gray-700/30 hover:border-blue-500/50'
                    }`}
                >
                  {tag}
                </Chip>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Featured Blogs */}
      {featuredBlogs.length > 0 && !searchQuery && !selectedTag && (
        <section className="relative py-16 px-4">
          {/* Background Effects */}
          <div className="absolute inset-0">
            <div className="absolute top-10 right-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
            <div className="absolute bottom-10 left-20 w-80 h-80 bg-green-500/5 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-6xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-3xl font-bold text-white mb-8 text-center"
            >
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                บทความแนะนำ
              </span>
            </motion.h2>
            <div className="grid md:grid-cols-3 gap-6">
              {featuredBlogs.map((blog, index) => (
                <motion.div
                  key={blog.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                >
                  <Link href={`/blogs/${encodeURIComponent(blog.slug)}`}>
                    <Card className="bg-gray-900/30 border border-gray-700/30 hover:border-blue-500/50 transition-all duration-300 cursor-pointer h-full backdrop-blur-sm hover:bg-gray-800/40 group">
                      {blog.featured_image && (
                        <div className="relative h-48 overflow-hidden">
                          <Image
                            src={blog.featured_image}
                            alt={blog.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      <CardBody className="p-6">
                        <h3 className="text-xl font-semibold text-white mb-2 line-clamp-2 group-hover:text-blue-300 transition-colors">
                          {blog.title}
                        </h3>
                        <p className="text-gray-400 text-sm mb-4 line-clamp-3">
                          {blog.excerpt}
                        </p>
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>{formatDate(blog.published_at || blog.created_at)}</span>
                          {blog.reading_time && (
                            <span className="flex items-center gap-1">
                              <span className="w-1 h-1 bg-blue-400 rounded-full"></span>
                              {blog.reading_time} นาทีในการอ่าน
                            </span>
                          )}
                        </div>
                      </CardBody>
                    </Card>
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* All Blogs */}
      <section className="relative py-16 px-4">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-6xl mx-auto">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-3xl font-bold text-white mb-8 text-center"
          >
            {searchQuery ? (
              <>ผลการค้นหา: <span className="text-blue-400">&ldquo;{searchQuery}&rdquo;</span></>
            ) : selectedTag ? (
              <>บทความในหมวด: <span className="text-green-400">{selectedTag}</span></>
            ) : (
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                บทความทั้งหมด
              </span>
            )}
          </motion.h2>

          {loading && blogs.length === 0 ? (
            <div className="text-center">
              <div className="inline-flex items-center gap-3 px-6 py-3 bg-gray-900/30 border border-gray-700/30 rounded-full backdrop-blur-sm">
                <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-gray-300">กำลังโหลด...</span>
              </div>
            </div>
          ) : blogs.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-2xl font-semibold text-white mb-2">ไม่พบบทความ</h3>
              <p className="text-gray-400">ลองค้นหาด้วยคำอื่นหรือเลือกหมวดหมู่ใหม่</p>
            </div>
          ) : (
            <>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {blogs.map((blog, index) => (
                  <motion.div
                    key={blog.id}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                  >
                    <Link href={`/blogs/${encodeURIComponent(blog.slug)}`}>
                      <Card className="bg-gray-900/30 border border-gray-700/30 hover:border-blue-500/50 transition-all duration-300 cursor-pointer h-full backdrop-blur-sm hover:bg-gray-800/40 group">
                        {blog.featured_image && (
                          <div className="relative h-48 overflow-hidden">
                            <Image
                              src={blog.featured_image}
                              alt={blog.title}
                              fill
                              className="object-cover"
                            />
                          </div>
                        )}
                        <CardBody className="p-6">
                          <div className="flex flex-wrap gap-1 mb-3">
                            {blog.tags.slice(0, 3).map((tag) => (
                              <Chip
                                key={tag}
                                size="sm"
                                className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-300 text-xs border border-blue-500/20"
                              >
                                {tag}
                              </Chip>
                            ))}
                          </div>
                          <h3 className="text-xl font-semibold text-white mb-2 line-clamp-2 group-hover:text-blue-300 transition-colors">
                            {blog.title}
                          </h3>
                          <p className="text-gray-400 text-sm mb-4 line-clamp-3">
                            {blog.excerpt}
                          </p>
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>{formatDate(blog.published_at || blog.created_at)}</span>
                            <div className="flex items-center gap-3">
                              {blog.reading_time && (
                                <span className="flex items-center gap-1">
                                  <span className="w-1 h-1 bg-blue-400 rounded-full"></span>
                                  {blog.reading_time} นาที
                                </span>
                              )}
                              <span className="flex items-center gap-1">
                                <span className="w-1 h-1 bg-green-400 rounded-full"></span>
                                {blog.view_count} views
                              </span>
                            </div>
                          </div>
                        </CardBody>
                      </Card>
                    </Link>
                  </motion.div>
                ))}
              </div>

              {hasMore && (
                <div className="text-center">
                  <Button
                    onPress={loadMore}
                    className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold px-8 py-3 shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 transition-all duration-300"
                    size="lg"
                  >
                    {loading ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        กำลังโหลด...
                      </div>
                    ) : (
                      'โหลดเพิ่มเติม'
                    )}
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      <FooterSection />
    </div>
  );
}
