# Multi-API Real-time Gold Price Setup

## 🚀 **Multi-API Architecture**

The gold price chart now uses multiple APIs with WebSocket streaming for maximum reliability and real-time updates:

1. **Finnhub** (Primary) - 60 calls/minute + WebSocket streaming
2. **Twelve Data** (Fallback) - 800 requests/day
3. **Binance WebSocket** (Real-time streaming, no API key needed)
4. **Alpha Vantage** (Legacy support)

## 📋 **Setup Instructions**

### 1. Copy Environment File
```bash
cp .env.example .env.local
```

### 2. Get API Keys

#### **Finnhub (Recommended Primary)**
- Visit: [https://finnhub.io/register](https://finnhub.io/register)
- Free tier: 60 calls/minute + WebSocket streaming
- Symbol: `OANDA:XAU_USD` (Gold/USD)

#### **Twelve Data (Recommended Fallback)**
- Visit: [https://twelvedata.com/pricing](https://twelvedata.com/pricing)
- Free tier: 800 requests/day
- Symbol: `XAUUSD` (Gold/USD)

#### **Alpha Vantage (Legacy)**
- Visit: [https://www.alphavantage.co/support/#api-key](https://www.alphavantage.co/support/#api-key)
- Free tier: 25 requests/day (limited)

### 3. Configure .env.local
```bash
# Primary API (Best rate limits + WebSocket)
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_key_here

# Fallback API (High daily limit)
NEXT_PUBLIC_TWELVE_DATA_API_KEY=your_twelve_data_key_here

# Legacy support
NEXT_PUBLIC_ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
```

### 4. Restart Development Server
```bash
npm run dev
```

## ⚡ **Real-time Features**

### **WebSocket Streaming**
- **Finnhub WebSocket**: Real-time gold price updates (if API key provided)
- **Binance WebSocket**: PAX Gold streaming (no API key needed)
- **Auto-reconnection**: Automatic reconnection on connection loss
- **Fallback Polling**: API polling every 30 seconds as backup

### **Multi-API Fallback Chain**
1. **Finnhub API** → Live gold prices with WebSocket streaming
2. **Twelve Data API** → High daily limit fallback
3. **Simulated Data** → Realistic price movements if all APIs fail

## 📊 **Data Sources**

| API | Symbol | Rate Limit | WebSocket | Cost |
|-----|--------|------------|-----------|------|
| Finnhub | `OANDA:XAU_USD` | 60/min | ✅ | Free |
| Twelve Data | `XAUUSD` | 800/day | ❌ | Free |
| Binance | `paxgusd` | Unlimited | ✅ | Free |
| Alpha Vantage | `GLD` | 25/day | ❌ | Free |

## 🔧 **Advanced Configuration**

### **WebSocket Only Mode**
For maximum real-time updates, the chart can run purely on WebSocket data:
- Binance WebSocket provides continuous PAX Gold updates
- No API rate limits
- Automatic reconnection
- Real-time chart movement

### **Production Recommendations**
1. **Get Finnhub API key** for best gold price accuracy
2. **Add Twelve Data key** for high-volume fallback
3. **Monitor WebSocket connections** in browser console
4. **Consider paid plans** for production use

## 🐛 **Troubleshooting**

### **Check Data Sources**
- Chart shows current data source in the UI
- Console logs show WebSocket connection status
- Error messages indicate which APIs are failing

### **Common Issues**
- **"Demo key" errors**: Add real API keys to .env.local
- **Rate limit exceeded**: Chart automatically falls back to next API
- **WebSocket disconnected**: Auto-reconnection after 5 seconds
- **All APIs failed**: Chart uses simulated data with realistic movements

### **Console Debugging**
```javascript
// Check WebSocket connections
console.log('Finnhub WebSocket:', finnhubWs?.readyState);
console.log('Binance WebSocket:', binanceWs?.readyState);
```

## 🎯 **Result**
Your gold price chart now has:
- ✅ **Real-time streaming** via WebSocket
- ✅ **Multiple API fallbacks** for reliability
- ✅ **High rate limits** (800+ requests/day)
- ✅ **Continuous price movement** for engaging UX
- ✅ **Zero downtime** with automatic fallbacks
