'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Card, CardBody, CardHeader, Button, Input } from '@heroui/react'
import { useAuth } from '@/contexts/AuthContext'

export default function AdminLoginPage() {
  const router = useRouter()
  const { signIn, user, loading, isAdmin } = useAuth()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Redirect if already authenticated as admin
  useEffect(() => {
    if (!loading && user && isAdmin) {
      router.push('/admin')
    }
  }, [user, isAdmin, loading, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      const { error } = await signIn(formData.email, formData.password)

      if (error) {
        setError(error.message)
      } else {
        // Check if user is admin after successful login
        // The useEffect above will handle the redirect
      }
    } catch (error) {
      console.error('Login error:', error)
      setError('เกิดข้อผิดพลาดในการเข้าสู่ระบบ')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    // Clear error when user starts typing
    if (error) setError('')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-white text-xl">กำลังโหลด...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-blue-900/20 to-black flex items-center justify-center p-4 overflow-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden -z-10">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse delay-2000"></div>

        {/* Floating Elements */}
        <div className="absolute top-20 right-20 opacity-10">
          <div className="w-32 h-20 border border-purple-500/30 rounded bg-black/20 backdrop-blur-sm"></div>
        </div>
        <div className="absolute bottom-32 left-20 opacity-10">
          <div className="w-24 h-16 border border-blue-500/30 rounded bg-black/20 backdrop-blur-sm"></div>
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative z-10 w-full max-w-md"
      >
        <Card className="bg-black/40 backdrop-blur-md border border-blue-500/20 shadow-2xl shadow-blue-500/10">
          <CardHeader className="text-center pb-6">
            <div className="w-full">
              {/* Logo */}
              <div className="flex justify-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                  <span className="text-white font-bold text-3xl">T</span>
                </div>
              </div>

              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-3">
                TraderBase Admin
              </h1>
              <p className="text-gray-300 text-lg">
                เข้าสู่ระบบจัดการเว็บไซต์
              </p>
              <p className="text-gray-400 text-sm mt-2">
                ระบบที่ออกแบบมาให้ใช้งานง่าย
              </p>
            </div>
          </CardHeader>

          <CardBody className="pt-4 px-8 pb-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Error Message */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-4 bg-red-500/10 border border-red-500/30 rounded-xl backdrop-blur-sm"
                >
                  <p className="text-red-300 text-sm text-center font-medium">{error}</p>
                </motion.div>
              )}

              {/* Email Input */}
              <Input
                type="email"
                label="อีเมล"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                classNames={{
                  input: "bg-black/30 text-white text-lg",
                  inputWrapper: "bg-black/30 border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 backdrop-blur-sm",
                  label: "text-gray-300 font-semibold"
                }}
                required
              />

              {/* Password Input */}
              <Input
                type="password"
                label="รหัสผ่าน"
                placeholder="••••••••"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                classNames={{
                  input: "bg-black/30 text-white text-lg",
                  inputWrapper: "bg-black/30 border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 backdrop-blur-sm",
                  label: "text-gray-300 font-semibold"
                }}
                required
              />

              {/* Login Button */}
              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold h-14 text-lg rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                disabled={isLoading || !formData.email || !formData.password}
              >
                {isLoading ? 'กำลังเข้าสู่ระบบ...' : 'เข้าสู่ระบบ'}
              </Button>
            </form>

            {/* Demo Credentials */}
            <div className="mt-8 p-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-xl backdrop-blur-sm">
              <h3 className="text-blue-300 font-bold mb-3 text-base">
                ข้อมูลสำหรับทดสอบ
              </h3>
              <div className="space-y-2 text-sm text-gray-200">
                <p className="flex items-center gap-2">
                  <span className="font-semibold text-blue-300">อีเมล:</span>
                  <code className="bg-black/30 px-2 py-1 rounded text-blue-200"><EMAIL></code>
                </p>
                <p className="flex items-center gap-2">
                  <span className="font-semibold text-purple-300">รหัสผ่าน:</span>
                  <code className="bg-black/30 px-2 py-1 rounded text-purple-200">admin123</code>
                </p>
                <p className="text-yellow-300 mt-3 text-xs bg-yellow-500/10 p-2 rounded-lg border border-yellow-500/20">
                  กรุณาสร้างบัญชีผู้ดูแลระบบใน Supabase Auth หรือใช้ข้อมูลทดสอบข้างต้น
                </p>
              </div>
            </div>

            {/* Back to Website */}
            <div className="mt-6 text-center">
              <Button
                variant="ghost"
                className="text-gray-400 hover:text-white hover:bg-white/10 transition-all duration-300 font-semibold"
                onPress={() => router.push('/')}
              >
                ← กลับไปเว็บไซต์หลัก
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* Instructions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mt-6 text-center"
        >
          <Card className="bg-black/10 border border-white/5">
            <CardBody className="p-4">
              <h3 className="text-white font-semibold mb-2 text-sm">
                📋 วิธีการตั้งค่าบัญชีผู้ดูแลระบบ:
              </h3>
              <div className="text-xs text-gray-400 space-y-1 text-left">
                <p>1. เข้าไปที่ Supabase Dashboard</p>
                <p>2. ไปที่ Authentication → Users</p>
                <p>3. สร้างผู้ใช้ใหม่หรือแก้ไขผู้ใช้ที่มีอยู่</p>
                <p>4. เพิ่ม metadata: <code className="bg-gray-800 px-1 rounded">{`{"role": "admin"}`}</code></p>
                <p>{`5. หรือใช้อีเมลที่มีคำว่า "admin"`}</p>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </motion.div>
    </div>
  )
}
