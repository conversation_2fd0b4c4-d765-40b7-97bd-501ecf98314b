'use client'

import { useReadStatus } from "@/hooks/useReadStatus";

export function useReadableAccordionItem(itemKey: string, title: string, stepPrefix: string) {
  const { isRead, toggleReadStatus, isClient } = useReadStatus();
  const itemId = `${stepPrefix}-${itemKey}`;
  const isItemRead = isClient ? isRead(itemId) : false;

  const titleElement = (
    <div className="flex items-center justify-between w-full">
      <span className={isItemRead ? 'text-green-400' : 'text-white'}>{title}</span>
      {isClient && (
        <div
          onClick={(e) => {
            e.stopPropagation();
            toggleReadStatus(itemId);
          }}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              e.stopPropagation();
              toggleReadStatus(itemId);
            }
          }}
          className={`ml-2 px-2 py-1 text-xs rounded-full transition-all duration-200 cursor-pointer ${isItemRead
            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
            : 'bg-gray-500/20 text-gray-400 border border-gray-500/30 hover:bg-blue-500/20 hover:text-blue-400'
            }`}
        >
          {isItemRead ? '✓ อ่านแล้ว' : 'ทำเครื่องหมายว่าอ่านแล้ว'}
        </div>
      )}
    </div>
  );

  return {
    'aria-label': title,
    title: titleElement,
    className: "text-white",
    isRead: isItemRead,
    itemKey: itemKey
  };
}
