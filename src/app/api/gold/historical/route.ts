import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const requestedSymbol = searchParams.get("symbol") || "PAXGUSDT";
    const interval = searchParams.get("interval") || "1d";
    const limit = searchParams.get("limit") || "30";

    // Try different gold-related symbols if the requested one fails
    const goldSymbols = [
      requestedSymbol,
      "PAXGUSDT", // PAX Gold (1 PAXG = 1 troy ounce)
      "XAUUSDT", // Gold futures (if available)
      "GOLDUSDT", // Alternative gold token
      "BTCUSDT", // BTC as final fallback
    ];

    let response;
    let symbol = requestedSymbol;

    for (const testSymbol of goldSymbols) {
      try {
        console.log(`📅 Trying symbol: ${testSymbol}`);

        response = await fetch(
          `https://api.binance.com/api/v3/klines?symbol=${testSymbol}&interval=${interval}&limit=${limit}`,
          {
            headers: {
              "User-Agent": "TraderBase/1.0",
            },
          }
        );

        if (response.ok) {
          console.error(response);
          symbol = testSymbol;
          console.log(`✅ Success with symbol: ${symbol}`);
          break;
        } else {
          console.log(
            `❌ Failed with symbol: ${testSymbol} - ${response.status}`
          );
        }
      } catch (error) {
        console.log(`❌ Error with symbol: ${testSymbol} - ${error}`);
        continue;
      }
    }

    if (!response || !response.ok) {
      throw new Error(`All symbols failed. Last status: ${response?.status}`);
    }

    const data = await response.json();

    // Debug the historical response structure
    console.log(
      "📊 Raw historical response sample:",
      JSON.stringify(data[0], null, 2)
    );
    console.log("📊 Historical data structure:", {
      isArray: Array.isArray(data),
      length: data.length,
      firstItemType: typeof data[0],
      firstItemStructure: data[0] ? Object.keys(data[0]) : "no keys",
      sampleClosePrice: data[0] ? data[0][4] : "no close price",
    });

    console.log(`📊 Retrieved ${data.length} historical data points`);

    // Return the data with CORS headers and symbol info
    return NextResponse.json(
      {
        data,
        symbol,
        message: `Data retrieved for ${symbol}`,
      },
      {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    console.error("❌ Historical data API error:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch historical data",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      {
        status: 500,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
