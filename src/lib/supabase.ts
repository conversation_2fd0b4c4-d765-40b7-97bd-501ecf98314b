import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Blog types
export interface Blog {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: BlogContent[]
  featured_image?: string
  author_name: string
  author_avatar?: string
  published: boolean
  featured: boolean
  view_count: number
  reading_time?: number
  tags: string[]
  meta_title?: string
  meta_description?: string
  created_at: string
  updated_at: string
  published_at?: string
}

// Blog content types (similar to Medium's editor structure)
export interface BlogContent {
  type: 'paragraph' | 'heading' | 'image' | 'quote' | 'list' | 'code'
  attrs?: {
    level?: number // for headings (1-6)
    src?: string // for images
    alt?: string // for images
    caption?: string // for images
    language?: string // for code blocks
  }
  content?: BlogContentText[]
}

export interface BlogContentText {
  type: 'text' | 'hard_break'
  text?: string
  marks?: BlogContentMark[]
}

export interface BlogContentMark {
  type: 'bold' | 'italic' | 'underline' | 'strike' | 'code' | 'link'
  attrs?: {
    href?: string // for links
    target?: string // for links
  }
}

// Blog API functions
export const blogApi = {
  // Get all published blogs with pagination
  async getBlogs(page = 1, limit = 10, featured = false) {
    const from = (page - 1) * limit
    const to = from + limit - 1

    let query = supabase
      .from('blogs')
      .select('*')
      .eq('published', true)
      .order('published_at', { ascending: false })

    if (featured) {
      query = query.eq('featured', true)
    }

    const { data, error, count } = await query
      .range(from, to)
      .limit(limit)

    if (error) throw error

    return {
      blogs: data as Blog[],
      totalCount: count || 0,
      hasMore: (count || 0) > to + 1
    }
  },

  // Get a single blog by slug
  async getBlogBySlug(slug: string) {
    const { data, error } = await supabase
      .from('blogs')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .single()

    if (error) throw error

    // Increment view count
    await supabase
      .from('blogs')
      .update({ view_count: (data.view_count || 0) + 1 })
      .eq('id', data.id)

    return data as Blog
  },

  // Get featured blogs
  async getFeaturedBlogs(limit = 3) {
    const { data, error } = await supabase
      .from('blogs')
      .select('*')
      .eq('published', true)
      .eq('featured', true)
      .order('published_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data as Blog[]
  },

  // Get blogs by tag
  async getBlogsByTag(tag: string, page = 1, limit = 10) {
    const from = (page - 1) * limit
    const to = from + limit - 1

    const { data, error, count } = await supabase
      .from('blogs')
      .select('*')
      .eq('published', true)
      .contains('tags', [tag])
      .order('published_at', { ascending: false })
      .range(from, to)
      .limit(limit)

    if (error) throw error

    return {
      blogs: data as Blog[],
      totalCount: count || 0,
      hasMore: (count || 0) > to + 1
    }
  },

  // Search blogs
  async searchBlogs(query: string, page = 1, limit = 10) {
    const from = (page - 1) * limit
    const to = from + limit - 1

    const { data, error, count } = await supabase
      .from('blogs')
      .select('*')
      .eq('published', true)
      .or(`title.ilike.%${query}%,excerpt.ilike.%${query}%`)
      .order('published_at', { ascending: false })
      .range(from, to)
      .limit(limit)

    if (error) throw error

    return {
      blogs: data as Blog[],
      totalCount: count || 0,
      hasMore: (count || 0) > to + 1
    }
  },

  // Get all unique tags
  async getAllTags() {
    const { data, error } = await supabase
      .from('blogs')
      .select('tags')
      .eq('published', true)

    if (error) throw error

    // Flatten and deduplicate tags
    const allTags = data.flatMap(blog => blog.tags || [])
    const uniqueTags = [...new Set(allTags)].sort()

    return uniqueTags
  }
}

// Utility functions
export const blogUtils = {
  // Calculate reading time based on content
  calculateReadingTime(content: BlogContent[]): number {
    const wordsPerMinute = 200
    let wordCount = 0

    content.forEach(block => {
      if (block.content) {
        block.content.forEach(textNode => {
          if (textNode.text) {
            wordCount += textNode.text.split(' ').length
          }
        })
      }
    })

    return Math.ceil(wordCount / wordsPerMinute)
  },

  // Extract plain text from blog content for SEO
  extractPlainText(content: BlogContent[]): string {
    let text = ''

    content.forEach(block => {
      if (block.content) {
        block.content.forEach(textNode => {
          if (textNode.text) {
            text += textNode.text + ' '
          }
        })
      }
    })

    return text.trim()
  },

  // Generate excerpt from content
  generateExcerpt(content: BlogContent[], maxLength = 160): string {
    const plainText = this.extractPlainText(content)
    return plainText.length > maxLength 
      ? plainText.substring(0, maxLength) + '...'
      : plainText
  }
}
