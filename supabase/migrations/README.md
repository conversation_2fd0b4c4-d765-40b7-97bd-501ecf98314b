# Supabase Storage Migrations

This directory contains SQL migration files for setting up the storage bucket and policies for the TraderBase project.

## Files

- `20241224000000_create_uploads_bucket.sql` - Creates the uploads bucket with RLS policies
- `20241224000001_rollback_uploads_bucket.sql` - Rollback script to remove the bucket and policies

## How to Apply Migrations

### Method 1: Using Supabase CLI (Recommended)

1. Make sure you have the Supabase CLI installed:
   ```bash
   npm install -g supabase
   ```

2. Initialize Supabase in your project (if not already done):
   ```bash
   supabase init
   ```

3. Link to your remote project:
   ```bash
   supabase link --project-ref YOUR_PROJECT_REF
   ```

4. Apply the migration:
   ```bash
   supabase db push
   ```

### Method 2: Manual Application via Dashboard

1. Go to your Supabase Dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the content of `20241224000000_create_uploads_bucket.sql`
4. Click **Run** to execute the migration

### Method 3: Using psql (Advanced)

```bash
psql "postgresql://postgres:[PASSWORD]@[HOST]:5432/postgres" -f 20241224000000_create_uploads_bucket.sql
```

## What This Migration Does

### 1. Creates Storage Bucket
- **Name**: `uploads`
- **Public**: `true` (allows public read access to files)
- **File Size Limit**: 5MB (5,242,880 bytes)
- **Allowed MIME Types**: 
  - `image/jpeg`
  - `image/jpg` 
  - `image/png`
  - `image/gif`
  - `image/webp`
  - `image/svg+xml`

### 2. Sets Up RLS Policies

#### Public Read Policy
- Allows anyone to view/download files from the uploads bucket
- Required for displaying images on the website

#### Admin Upload Policy
- Only allows authenticated users with admin privileges to upload files
- Admin detection methods:
  - Email contains "admin" (e.g., `<EMAIL>`)
  - `user_metadata.role = "admin"`
  - `app_metadata.role = "admin"`

#### Admin Update Policy
- Allows admin users to update files they own
- Prevents admins from modifying other admins' files

#### Admin Delete Policy
- Allows admin users to delete files they own
- Prevents accidental deletion of other users' files

### 3. Performance Optimizations
- Creates indexes on frequently queried columns
- Grants necessary permissions to authenticated users

## Admin User Setup

To make a user an admin, you have several options:

### Option 1: Email-based (Simplest)
Create a user with an email containing "admin":
- `<EMAIL>`
- `<EMAIL>`

### Option 2: User Metadata
Set the role in user metadata via Supabase Dashboard:
1. Go to **Authentication** → **Users**
2. Click on the user
3. In **User Metadata**, add:
   ```json
   {
     "role": "admin"
   }
   ```

### Option 3: App Metadata (Requires API)
Use the Supabase Admin API to set app metadata:
```javascript
const { data, error } = await supabase.auth.admin.updateUserById(
  userId,
  { app_metadata: { role: 'admin' } }
)
```

## Testing the Setup

1. **Create an admin user** using one of the methods above
2. **Login to your application** with the admin account
3. **Try uploading an image** in the blog editor
4. **Check the uploads bucket** in Supabase Dashboard to see the uploaded file

## Troubleshooting

### Common Issues

1. **403 Unauthorized Error**
   - Check that the user is properly authenticated
   - Verify admin role is set correctly
   - Ensure RLS policies are applied

2. **Bucket Not Found Error**
   - Run the migration to create the bucket
   - Check that the bucket name matches in your code (`uploads`)

3. **File Size Too Large**
   - Default limit is 5MB
   - Adjust `file_size_limit` in the migration if needed

4. **MIME Type Not Allowed**
   - Check the `allowed_mime_types` array in the migration
   - Add additional types if needed

### Verification Queries

Check if bucket exists:
```sql
SELECT * FROM storage.buckets WHERE id = 'uploads';
```

Check policies:
```sql
SELECT * FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage';
```

Check user permissions:
```sql
SELECT 
  auth.jwt() ->> 'email' as email,
  auth.jwt() -> 'user_metadata' ->> 'role' as user_role,
  auth.jwt() -> 'app_metadata' ->> 'role' as app_role;
```

## Rollback

If you need to remove the bucket and policies:

```bash
# Using Supabase CLI
supabase db reset

# Or manually run the rollback script
psql "postgresql://..." -f 20241224000001_rollback_uploads_bucket.sql
```

⚠️ **Warning**: Rollback will delete all uploaded files permanently!
