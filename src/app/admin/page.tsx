'use client'

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>H<PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";
import { blogApi, Blog } from "@/lib/supabase";
import Link from "next/link";

interface DashboardStats {
  totalBlogs: number;
  publishedBlogs: number;
  draftBlogs: number;
  totalViews: number;
  featuredBlogs: number;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalBlogs: 0,
    publishedBlogs: 0,
    draftBlogs: 0,
    totalViews: 0,
    featuredBlogs: 0
  });
  const [recentBlogs, setRecentBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Get recent blogs (you'll need to create an admin API function)
      const recentData = await blogApi.getBlogs(1, 5);
      setRecentBlogs(recentData.blogs);

      // Calculate stats from the blogs
      // Note: In a real app, you'd want to create specific admin API endpoints for these stats
      const allBlogsData = await blogApi.getBlogs(1, 1000); // Get all blogs for stats
      const blogs = allBlogsData.blogs;

      const dashboardStats: DashboardStats = {
        totalBlogs: blogs.length,
        publishedBlogs: blogs.filter(blog => blog.published).length,
        draftBlogs: blogs.filter(blog => !blog.published).length,
        totalViews: blogs.reduce((sum, blog) => sum + blog.view_count, 0),
        featuredBlogs: blogs.filter(blog => blog.featured).length
      };

      setStats(dashboardStats);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const statCards = [
    {
      title: 'บทความทั้งหมด',
      value: stats.totalBlogs,
      icon: '▣',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'บทความที่เผยแพร่',
      value: stats.publishedBlogs,
      icon: '✓',
      color: 'from-green-500 to-green-600'
    },
    {
      title: 'บทความร่าง',
      value: stats.draftBlogs,
      icon: '□',
      color: 'from-yellow-500 to-yellow-600'
    },
    {
      title: 'ยอดเข้าชมรวม',
      value: stats.totalViews.toLocaleString(),
      icon: '◉',
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: 'บทความแนะนำ',
      value: stats.featuredBlogs,
      icon: '★',
      color: 'from-orange-500 to-orange-600'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-white text-xl">กำลังโหลด...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card className="bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-blue-500/20 border border-blue-500/30 backdrop-blur-sm shadow-2xl shadow-blue-500/10">
          <CardBody className="p-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-3">
                  ยินดีต้อนรับสู่ระบบจัดการ
                </h1>
                <p className="text-gray-200 text-xl leading-relaxed">
                  จัดการเนื้อหาบทความและการตั้งค่าของ <span className="text-blue-400 font-semibold">TraderBase</span> อย่างง่ายดาย
                </p>
                <p className="text-gray-300 mt-2">
                  ระบบที่ออกแบบมาให้ใช้งานง่าย เหมาะสำหรับผู้ที่ไม่มีความรู้ด้านเทคนิค
                </p>
              </div>
              <div className="text-6xl text-blue-400">●</div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
          >
            <Card className="bg-black/30 backdrop-blur-sm border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105">
              <CardBody className="p-6">
                <div className="flex items-center gap-4">
                  <div className={`w-14 h-14 rounded-xl bg-gradient-to-r ${stat.color} flex items-center justify-center text-3xl shadow-lg`}>
                    {stat.icon}
                  </div>
                  <div>
                    <p className="text-gray-300 text-sm font-medium">{stat.title}</p>
                    <p className="text-3xl font-bold text-white">{stat.value}</p>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card className="bg-black/30 backdrop-blur-sm border border-purple-500/20 shadow-lg shadow-purple-500/10">
          <CardHeader className="pb-4">
            <h3 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              การดำเนินการด่วน
            </h3>
            <p className="text-gray-300 mt-1">เครื่องมือที่ใช้บ่อยสำหรับการจัดการเว็บไซต์</p>
          </CardHeader>
          <CardBody className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Link href="/admin/blogs/new">
                <Button className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold h-20 rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                  <div className="flex flex-col items-center gap-2">
                    <span className="text-3xl">✚</span>
                    <span className="text-lg">เขียนบทความใหม่</span>
                  </div>
                </Button>
              </Link>
              <Link href="/admin/blogs">
                <Button className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold h-20 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                  <div className="flex flex-col items-center gap-2">
                    <span className="text-3xl">▣</span>
                    <span className="text-lg">จัดการบทความ</span>
                  </div>
                </Button>
              </Link>
              <Link href="/blogs" target="_blank">
                <Button className="w-full bg-gradient-to-r from-green-500 to-blue-500 text-white font-semibold h-20 rounded-xl hover:from-green-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                  <div className="flex flex-col items-center gap-2">
                    <span className="text-3xl">◉</span>
                    <span className="text-lg">ดูเว็บไซต์</span>
                  </div>
                </Button>
              </Link>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Recent Blogs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card className="bg-black/30 backdrop-blur-sm border border-green-500/20 shadow-lg shadow-green-500/10">
          <CardHeader className="flex justify-between items-center pb-4">
            <div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                บทความล่าสุด
              </h3>
              <p className="text-gray-300 mt-1">บทความที่เพิ่งสร้างหรือแก้ไขล่าสุด</p>
            </div>
            <Link href="/admin/blogs">
              <Button variant="ghost" className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10 transition-all duration-300 font-semibold">
                ดูทั้งหมด →
              </Button>
            </Link>
          </CardHeader>
          <CardBody className="p-6">
            <div className="space-y-4">
              {recentBlogs.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4 text-gray-400">▣</div>
                  <p className="text-gray-400 text-lg">ยังไม่มีบทความ</p>
                  <p className="text-gray-500 text-sm mt-2">เริ่มต้นสร้างบทความแรกของคุณได้เลย!</p>
                  <Link href="/admin/blogs/new" className="inline-block mt-4">
                    <Button className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold">
                      สร้างบทความใหม่
                    </Button>
                  </Link>
                </div>
              ) : (
                recentBlogs.map((blog) => (
                  <div
                    key={blog.id}
                    className="flex items-center justify-between p-6 bg-white/5 rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 hover:bg-white/10"
                  >
                    <div className="flex-1">
                      <h4 className="text-white font-semibold text-lg mb-2">{blog.title}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-300">
                        <span className="flex items-center gap-1">
                          {formatDate(blog.created_at)}
                        </span>
                        <span className="flex items-center gap-1">
                          {blog.view_count} views
                        </span>
                        <span className={`px-3 py-1 rounded-full text-xs font-semibold ${blog.published
                          ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                          : 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
                          }`}>
                          {blog.published ? 'เผยแพร่' : 'ร่าง'}
                        </span>
                        {blog.featured && (
                          <span className="px-3 py-1 rounded-full text-xs font-semibold bg-orange-500/20 text-orange-300 border border-orange-500/30">
                            แนะนำ
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Link href={`/admin/blogs/edit/${blog.id}`}>
                        <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10 font-semibold">
                          แก้ไข
                        </Button>
                      </Link>
                      <Link href={`/blogs/${encodeURIComponent(blog.slug)}`} target="_blank">
                        <Button size="sm" variant="ghost" className="text-gray-400 hover:text-gray-300 hover:bg-gray-500/10 font-semibold">
                          ดู
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
}
