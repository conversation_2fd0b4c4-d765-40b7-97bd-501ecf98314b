import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "4 ขั้นตอนเรียนรู้เทรด FOREX - TraderBase",
  description: "เรียนรู้การเทรด FOREX ใน 4 ขั้นตอนง่ายๆ ตั้งแต่พื้นฐาน การเลือกโบรกเกอร์ เครื่องมือเทรด และระบบเทรดมืออาชีพ",
  keywords: "FOREX, เทรด, การเงิน, โบรกเกอร์, ระบบเทรด, การลงทุน, ไทย, 4 ขั้นตอน",
  openGraph: {
    title: "4 ขั้นตอนเรียนรู้เทรด FOREX - TraderBase",
    description: "เรียนรู้การเทรด FOREX ใน 4 ขั้นตอนง่ายๆ ตั้งแต่พื้นฐาน การเลือกโบรกเกอร์ เครื่องมือเทรด และระบบเทรดมืออาชีพ",
    url: 'https://traderbaseth.com/forex-trading-steps',
    siteName: 'TraderBase',
    images: [
      {
        url: '/images/seo.png',
        width: 1200,
        height: 630,
        alt: '4 ขั้นตอนเรียนรู้เทรด FOREX - TraderBase',
      },
    ],
    locale: 'th_TH',
    type: 'article',
  },
  twitter: {
    card: 'summary_large_image',
    title: "4 ขั้นตอนเรียนรู้เทรด FOREX - TraderBase",
    description: "เรียนรู้การเทรด FOREX ใน 4 ขั้นตอนง่ายๆ ตั้งแต่พื้นฐาน การเลือกโบรกเกอร์ เครื่องมือเทรด และระบบเทรดมืออาชีพ",
    images: ['/images/seo.png'],
    creator: '@TraderBase',
  },
  alternates: {
    canonical: '/forex-trading-steps',
  },
};

export default function ForexTradingStepsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
