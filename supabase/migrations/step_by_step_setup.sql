-- Step-by-Step Setup for Uploads Bucket
-- Run each section separately in Supabase Dashboard SQL Editor

-- ============================================================================
-- STEP 1: Create the uploads bucket
-- Copy and run this first:
-- ============================================================================

INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'uploads',
  'uploads',
  true,
  5242880,
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
)
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- STEP 2: Create public read policy
-- Copy and run this second:
-- ============================================================================

CREATE POLICY "Public read access for uploads bucket"
ON storage.objects
FOR SELECT
USING (bucket_id = 'uploads');

-- ============================================================================
-- STEP 3: Create admin upload policy
-- Copy and run this third:
-- ============================================================================

CREATE POLICY "Admin upload access for uploads bucket"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'uploads' 
  AND auth.role() = 'authenticated'
  AND (
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
);

-- ============================================================================
-- STEP 4: Create admin update policy
-- Copy and run this fourth:
-- ============================================================================

CREATE POLICY "Admin update access for uploads bucket"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'uploads'
  AND auth.role() = 'authenticated'
  AND (
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
  AND owner = auth.uid()
)
WITH CHECK (
  bucket_id = 'uploads'
  AND auth.role() = 'authenticated'
  AND (
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
);

-- ============================================================================
-- STEP 5: Create admin delete policy
-- Copy and run this fifth:
-- ============================================================================

CREATE POLICY "Admin delete access for uploads bucket"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'uploads'
  AND auth.role() = 'authenticated'
  AND (
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
  AND owner = auth.uid()
);

-- ============================================================================
-- STEP 6: Verify setup (Optional)
-- Copy and run this last to check everything worked:
-- ============================================================================

-- Check if bucket exists
SELECT 
  id, 
  name, 
  public, 
  file_size_limit, 
  allowed_mime_types 
FROM storage.buckets 
WHERE id = 'uploads';

-- Check if policies exist
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'objects' 
  AND schemaname = 'storage'
  AND policyname LIKE '%uploads bucket%'
ORDER BY policyname;
