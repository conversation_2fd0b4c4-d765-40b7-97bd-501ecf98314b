import { supabase } from "./supabase";
import { Blog, BlogContent } from "./supabase";

// Helper function to check if user is authenticated and is admin
const checkAdminAuth = async () => {
  const {
    data: { session },
    error,
  } = await supabase.auth.getSession();

  if (error || !session) {
    throw new Error("ไม่ได้รับอนุญาตให้เข้าถึง กรุณาเข้าสู่ระบบ");
  }

  // Check if user is admin
  const user = session.user;
  const isAdmin =
    user?.user_metadata?.role === "admin" ||
    user?.app_metadata?.role === "admin";

  if (!isAdmin) {
    throw new Error("คุณไม่มีสิทธิ์ในการดำเนินการนี้");
  }

  return session;
};

// Admin-specific blog API functions
export const adminBlogApi = {
  // Get all blogs (including drafts) with pagination
  async getAllBlogs(
    page = 1,
    limit = 10,
    status: "all" | "published" | "draft" = "all"
  ) {
    await checkAdminAuth();

    const from = (page - 1) * limit;
    const to = from + limit - 1;

    let query = supabase
      .from("blogs")
      .select("*")
      .order("created_at", { ascending: false });

    if (status === "published") {
      query = query.eq("published", true);
    } else if (status === "draft") {
      query = query.eq("published", false);
    }

    const { data, error, count } = await query.range(from, to).limit(limit);

    if (error) throw error;

    return {
      blogs: data as Blog[],
      totalCount: count || 0,
      hasMore: (count || 0) > to + 1,
    };
  },

  // Get a single blog by ID (including drafts)
  async getBlogById(id: string) {
    const { data, error } = await supabase
      .from("blogs")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;
    return data as Blog;
  },

  // Create a new blog
  async createBlog(blogData: Partial<Blog>) {
    await checkAdminAuth();

    const { data, error } = await supabase
      .from("blogs")
      .insert([
        {
          ...blogData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return data as Blog;
  },

  // Update a blog
  async updateBlog(id: string, updates: Partial<Blog>) {
    await checkAdminAuth();

    const { data, error } = await supabase
      .from("blogs")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    return data as Blog;
  },

  // Delete a blog
  async deleteBlog(id: string) {
    await checkAdminAuth();

    const { error } = await supabase.from("blogs").delete().eq("id", id);

    if (error) throw error;
    return true;
  },

  // Search blogs (including drafts)
  async searchBlogs(query: string, page = 1, limit = 10) {
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await supabase
      .from("blogs")
      .select("*")
      .or(`title.ilike.%${query}%,excerpt.ilike.%${query}%`)
      .order("created_at", { ascending: false })
      .range(from, to)
      .limit(limit);

    if (error) throw error;

    return {
      blogs: data as Blog[],
      totalCount: count || 0,
      hasMore: (count || 0) > to + 1,
    };
  },

  // Duplicate a blog
  async duplicateBlog(id: string) {
    const originalBlog = await this.getBlogById(id);

    const duplicatedBlog = {
      ...originalBlog,
      title: `${originalBlog.title} (Copy)`,
      slug: `${originalBlog.slug}-copy-${Date.now()}`,
      published: false,
      featured: false,
      view_count: 0,
      published_at: undefined,
    };

    // Remove the id to create a new record
    delete (duplicatedBlog as any).id;
    delete (duplicatedBlog as any).created_at;
    delete (duplicatedBlog as any).updated_at;

    return this.createBlog(duplicatedBlog);
  },

  // Bulk operations
  async bulkUpdateStatus(ids: string[], published: boolean) {
    const updates: any = {
      published,
      updated_at: new Date().toISOString(),
    };

    if (published) {
      updates.published_at = new Date().toISOString();
    } else {
      updates.published_at = undefined;
    }

    const { data, error } = await supabase
      .from("blogs")
      .update(updates)
      .in("id", ids)
      .select();

    if (error) throw error;
    return data as Blog[];
  },

  async bulkDelete(ids: string[]) {
    const { error } = await supabase.from("blogs").delete().in("id", ids);

    if (error) throw error;
    return true;
  },
};

// Blog content utilities for admin
export const adminBlogUtils = {
  // Validate blog content
  validateBlogContent(content: BlogContent[]): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!content || content.length === 0) {
      errors.push("เนื้อหาบทความไม่สามารถว่างเปล่าได้");
    }

    content.forEach((block, index) => {
      if (!block.type) {
        errors.push(`บล็อกที่ ${index + 1}: ไม่มีประเภทของเนื้อหา`);
      }

      if (block.type === "image" && !block.attrs?.src) {
        errors.push(`บล็อกที่ ${index + 1}: รูปภาพต้องมี URL`);
      }

      if (
        block.type === "heading" &&
        (!block.attrs?.level || block.attrs.level < 1 || block.attrs.level > 6)
      ) {
        errors.push(`บล็อกที่ ${index + 1}: หัวข้อต้องมีระดับ 1-6`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  // Generate SEO-friendly slug that supports Thai characters
  generateSlug(title: string): string {
    return (
      title
        .toLowerCase()
        // Keep Thai characters, English letters, numbers, spaces, and hyphens
        .replace(/[^\u0E00-\u0E7F\w\s-]/g, "") // Remove special characters but keep Thai
        .replace(/\s+/g, "-") // Replace spaces with hyphens
        .replace(/-+/g, "-") // Replace multiple hyphens with single
        .replace(/^-+|-+$/g, "") // Remove leading/trailing hyphens
        .trim()
    );
  },

  // Extract first image from content
  extractFirstImage(content: BlogContent[]): string | null {
    for (const block of content) {
      if (block.type === "image" && block.attrs?.src) {
        return block.attrs.src;
      }
    }
    return null;
  },

  // Count words in content
  countWords(content: BlogContent[]): number {
    let wordCount = 0;

    content.forEach((block) => {
      if (block.content) {
        block.content.forEach((textNode) => {
          if (textNode.text) {
            wordCount += textNode.text.split(/\s+/).length;
          }
        });
      }
    });

    return wordCount;
  },

  // Generate excerpt from content
  generateExcerpt(content: BlogContent[], maxLength = 160): string {
    let text = "";

    for (const block of content) {
      if (block.type === "paragraph" && block.content) {
        for (const textNode of block.content) {
          if (textNode.text) {
            text += textNode.text + " ";
            if (text.length > maxLength) break;
          }
        }
        if (text.length > maxLength) break;
      }
    }

    return text.length > maxLength
      ? text.substring(0, maxLength).trim() + "..."
      : text.trim();
  },
};
