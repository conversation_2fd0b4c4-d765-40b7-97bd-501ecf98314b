import { supabase } from "./supabase";

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export class SupabaseStorage {
  private static readonly BUCKET_NAME = "uploads";

  /**
   * Initialize storage bucket (call this once during setup)
   */
  static async initializeBucket(): Promise<void> {
    try {
      // Check if bucket exists
      const { data: buckets } = await supabase.storage.listBuckets();
      const bucketExists = buckets?.some(
        (bucket) => bucket.name === this.BUCKET_NAME
      );

      if (!bucketExists) {
        // Create bucket if it doesn't exist
        const { error } = await supabase.storage.createBucket(
          this.BUCKET_NAME,
          {
            public: true,
            allowedMimeTypes: [
              "image/jpeg",
              "image/png",
              "image/gif",
              "image/webp",
            ],
            fileSizeLimit: 5242880, // 5MB
          }
        );

        if (error) {
          console.error("Error creating bucket:", error);
        } else {
          console.log("Blog images bucket created successfully");
        }
      }
    } catch (error) {
      console.error("Error initializing bucket:", error);
    }
  }

  /**
   * Upload an image file to Supabase Storage
   */
  static async uploadImage(
    file: File,
    folder: string = "uploads"
  ): Promise<UploadResult> {
    try {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        return {
          success: false,
          error: "ไฟล์ต้องเป็นรูปภาพเท่านั้น",
        };
      }

      // Validate file size (5MB max)
      if (file.size > 5242880) {
        return {
          success: false,
          error: "ขนาดไฟล์ต้องไม่เกิน 5MB",
        };
      }

      // Generate unique filename
      const fileExt = file.name.split(".").pop();
      const fileName = `${Date.now()}-${Math.random()
        .toString(36)
        .substring(2)}.${fileExt}`;
      const filePath = `${folder}/${fileName}`;

      // Upload file
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: false,
        });

      if (error) {
        console.error("Upload error:", error);

        // Provide more specific error messages
        let errorMessage = "เกิดข้อผิดพลาดในการอัปโหลดรูปภาพ";

        if (error.message?.includes("bucket")) {
          errorMessage = `ไม่พบ Storage Bucket "${this.BUCKET_NAME}" กรุณาสร้างใน Supabase Dashboard`;
        } else if (error.message?.includes("policy")) {
          errorMessage = "ไม่มีสิทธิ์อัปโหลด กรุณาตรวจสอบ RLS Policy";
        } else if (error.message?.includes("size")) {
          errorMessage = "ขนาดไฟล์เกินกำหนด";
        }

        return {
          success: false,
          error: errorMessage,
        };
      }

      // Get public URL
      const {
        data: { publicUrl },
      } = supabase.storage.from(this.BUCKET_NAME).getPublicUrl(filePath);

      return {
        success: true,
        url: publicUrl,
      };
    } catch (error) {
      console.error("Upload error:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "เกิดข้อผิดพลาดในการอัปโหลดรูปภาพ",
      };
    }
  }

  /**
   * Delete an image from Supabase Storage
   */
  static async deleteImage(imageUrl: string): Promise<boolean> {
    try {
      // Extract file path from URL
      const url = new URL(imageUrl);
      const pathParts = url.pathname.split("/");
      const bucketIndex = pathParts.findIndex(
        (part) => part === this.BUCKET_NAME
      );

      if (bucketIndex === -1) {
        console.error("Invalid image URL - bucket not found");
        return false;
      }

      const filePath = pathParts.slice(bucketIndex + 1).join("/");

      if (!filePath) {
        console.error("Invalid image URL - no file path");
        return false;
      }

      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);

      if (error) {
        console.error("Delete error:", error);
        return false;
      }

      return true;
    } catch (error) {
      console.error("Delete error:", error);
      return false;
    }
  }

  /**
   * Get optimized image URL with transformations
   */
  static getOptimizedImageUrl(
    imageUrl: string,
    options: {
      width?: number;
      height?: number;
      quality?: number;
      format?: "webp" | "jpeg" | "png";
    } = {}
  ): string {
    try {
      const url = new URL(imageUrl);
      const params = new URLSearchParams();

      if (options.width) params.set("width", options.width.toString());
      if (options.height) params.set("height", options.height.toString());
      if (options.quality) params.set("quality", options.quality.toString());
      if (options.format) params.set("format", options.format);

      if (params.toString()) {
        url.search = params.toString();
      }

      return url.toString();
    } catch (error) {
      console.error("Error creating optimized URL:", error);
      return imageUrl;
    }
  }

  /**
   * Check if user has permission to upload images
   */
  static async checkUploadPermission(): Promise<boolean> {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session) {
        return false;
      }

      // Check if user is admin by email or metadata
      const user = session.user;
      const isAdmin =
        user?.email?.includes("admin") ||
        user?.user_metadata?.role === "admin" ||
        user?.app_metadata?.role === "admin";

      return isAdmin;
    } catch (error) {
      console.error("Error checking upload permission:", error);
      return false;
    }
  }
}

// Note: Make sure the 'uploads' bucket exists in your Supabase Storage
// You can create it manually in the Supabase dashboard
