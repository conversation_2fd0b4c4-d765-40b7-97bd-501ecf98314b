'use client'

import React, { createElement } from "react";
import Image from "next/image";
import { BlogContent, BlogContentText } from "@/lib/supabase";

interface BlogContentRendererProps {
  content: BlogContent[];
}

export default function BlogContentRenderer({ content }: BlogContentRendererProps) {
  // Check if content contains HTML from React18QuillEditor
  const isHtmlContent = (content: BlogContent[]): boolean => {
    return content.length === 1 &&
      content[0].type === 'paragraph' &&
      (content[0].content?.[0]?.text?.includes('__HTML_CONTENT__') ?? false);
  };

  // Extract HTML content from the wrapped format
  const extractHtmlContent = (content: BlogContent[]): string => {
    if (isHtmlContent(content)) {
      const htmlText = content[0].content?.[0]?.text || '';
      return htmlText.replace(/__HTML_CONTENT__/g, '');
    }
    return '';
  };

  // If this is HTML content from React18QuillEditor, render it directly
  if (isHtmlContent(content)) {
    const htmlContent = extractHtmlContent(content);
    return (
      <div className="prose prose-invert max-w-none">
        <div
          className="text-gray-300 leading-relaxed"
          dangerouslySetInnerHTML={{ __html: htmlContent }}
          style={{
            // Override Quill's default styles to match our theme
            color: '#d1d5db',
            fontFamily: 'inherit'
          }}
        />
        <style jsx>{`
          div :global(h1) { @apply text-4xl font-bold text-white mb-8 mt-12; }
          div :global(h2) { @apply text-3xl font-bold text-white mb-6 mt-10; }
          div :global(h3) { @apply text-2xl font-semibold text-white mb-4 mt-8; }
          div :global(h4) { @apply text-xl font-semibold text-white mb-4 mt-6; }
          div :global(h5) { @apply text-lg font-semibold text-white mb-3 mt-4; }
          div :global(h6) { @apply text-base font-semibold text-white mb-3 mt-4; }
          div :global(p) { @apply text-gray-300 leading-relaxed mb-6; }
          div :global(strong) { @apply text-white font-bold; }
          div :global(em) { @apply text-gray-200 italic; }
          div :global(u) { @apply underline; }
          div :global(s) { @apply line-through; }
          div :global(a) { @apply text-blue-400 hover:text-blue-300 underline transition-colors; }
          div :global(blockquote) { @apply border-l-4 border-blue-500 pl-6 py-4 my-8 bg-blue-500/5 rounded-r-lg text-gray-300 text-lg italic; }
          /* List styling - override Tailwind reset */
          div :global(ul) {
            color: #d1d5db !important;
            margin-bottom: 1.5rem !important;
            padding-left: 1.5rem !important;
            list-style-type: disc !important;
            list-style-position: outside !important;
          }
          div :global(ol) {
            color: #d1d5db !important;
            margin-bottom: 1.5rem !important;
            padding-left: 1.5rem !important;
            list-style-type: decimal !important;
            list-style-position: outside !important;
          }
          div :global(li) {
            color: #d1d5db !important;
            margin-bottom: 0.5rem !important;
            display: list-item !important;
            list-style: inherit !important;
          }
          /* Specific Quill editor list styling */
          div :global(.ql-editor ul),
          div :global(ul[data-list]) {
            color: #d1d5db !important;
            margin-bottom: 1.5rem !important;
            padding-left: 1.5rem !important;
            list-style-type: disc !important;
            list-style-position: outside !important;
          }
          div :global(.ql-editor ol),
          div :global(ol[data-list]) {
            color: #d1d5db !important;
            margin-bottom: 1.5rem !important;
            padding-left: 1.5rem !important;
            list-style-type: decimal !important;
            list-style-position: outside !important;
          }
          div :global(.ql-editor li),
          div :global(li[data-list]) {
            color: #d1d5db !important;
            margin-bottom: 0.5rem !important;
            display: list-item !important;
            list-style: inherit !important;
          }
          div :global(code) { @apply bg-gray-800 text-green-400 px-2 py-1 rounded text-sm font-mono; }
          div :global(pre) { @apply bg-gray-900 border border-gray-700 rounded-lg p-6 overflow-x-auto my-8; }
          div :global(pre code) { @apply text-green-400 font-mono text-sm bg-transparent p-0; }
          div :global(img) { @apply max-w-full h-auto rounded-lg my-8; }
          div :global(.video-wrapper) { @apply my-8; }
          div :global(.video-wrapper iframe) { @apply rounded-lg shadow-lg; }
        `}</style>
      </div>
    );
  }

  const renderTextContent = (textContent: BlogContentText[]) => {
    return textContent.map((textNode, index) => {
      if (textNode.type === 'hard_break') {
        return <br key={index} />;
      }

      if (!textNode.text) return null;

      let element = <span key={index}>{textNode.text}</span>;

      // Apply text marks (bold, italic, etc.)
      if (textNode.marks) {
        textNode.marks.forEach((mark) => {
          switch (mark.type) {
            case 'bold':
              element = <strong key={`${index}-bold`}>{element}</strong>;
              break;
            case 'italic':
              element = <em key={`${index}-italic`}>{element}</em>;
              break;
            case 'underline':
              element = <u key={`${index}-underline`}>{element}</u>;
              break;
            case 'strike':
              element = <s key={`${index}-strike`}>{element}</s>;
              break;
            case 'code':
              element = (
                <code
                  key={`${index}-code`}
                  className="bg-gray-800 text-green-400 px-2 py-1 rounded text-sm font-mono"
                >
                  {element}
                </code>
              );
              break;
            case 'link':
              element = (
                <a
                  key={`${index}-link`}
                  href={mark.attrs?.href}
                  target={mark.attrs?.target || '_blank'}
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 underline transition-colors"
                >
                  {element}
                </a>
              );
              break;
          }
        });
      }

      return element;
    });
  };

  const renderBlock = (block: BlogContent, index: number) => {
    switch (block.type) {
      case 'paragraph':
        return (
          <p key={index} className="text-gray-300 leading-relaxed mb-6 whitespace-break-spaces">
            {block.content ? renderTextContent(block.content) : ''}
          </p>
        );

      case 'heading':
        const level = block.attrs?.level || 1;
        const headingTag = `h${level}`;
        const headingClasses = {
          1: 'text-4xl font-bold text-white mb-8 mt-12',
          2: 'text-3xl font-bold text-white mb-6 mt-10',
          3: 'text-2xl font-semibold text-white mb-4 mt-8',
          4: 'text-xl font-semibold text-white mb-4 mt-6',
          5: 'text-lg font-semibold text-white mb-3 mt-4',
          6: 'text-base font-semibold text-white mb-3 mt-4'
        };

        return createElement(
          headingTag,
          {
            key: index,
            className: headingClasses[level as keyof typeof headingClasses]
          },
          block.content ? renderTextContent(block.content) : ''
        );

      case 'image':
        return (
          <div key={index} className="my-8">
            <div className="relative w-full h-64 md:h-96 rounded-lg overflow-hidden">
              <Image
                src={block.attrs?.src || ''}
                alt={block.attrs?.alt || ''}
                fill
                className="object-cover"
              />
            </div>
            {block.attrs?.caption && (
              <p className="text-center text-gray-400 text-sm mt-3 italic">
                {block.attrs.caption}
              </p>
            )}
          </div>
        );

      case 'quote':
        return (
          <blockquote key={index} className="border-l-4 border-blue-500 pl-6 py-4 my-8 bg-blue-500/5 rounded-r-lg whitespace-break-spaces">
            <div className="text-gray-300 text-lg italic">
              {block.content ? renderTextContent(block.content) : ''}
            </div>
          </blockquote>
        );

      case 'list':
        // For simplicity, treating all lists as unordered. You can extend this for ordered lists
        return (
          <ul key={index} className="list-disc list-inside text-gray-300 mb-6 space-y-2">
            {block.content?.map((item, itemIndex) => (
              <li key={itemIndex}>
                {renderTextContent([item])}
              </li>
            ))}
          </ul>
        );

      case 'code':
        return (
          <div key={index} className="my-8">
            <pre className="bg-gray-900 border border-gray-700 rounded-lg p-6 overflow-x-auto whitespace-break-spaces">
              <code className={`text-green-400 font-mono text-sm whitespace-break-spaces ${block.attrs?.language ? `language-${block.attrs.language}` : ''}`}>
                {block.content ? renderTextContent(block.content) : ''}
              </code>
            </pre>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="prose prose-invert max-w-none">
      {content.map((block, index) => renderBlock(block, index))}
    </div>
  );
}
