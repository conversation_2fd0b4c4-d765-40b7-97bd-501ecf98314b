'use client'

import { useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Textarea,
  Switch,
  Chip
} from "@heroui/react";
import { BlogContent, Blog } from "@/lib/supabase";

// Extended Blog interface for HTML content
interface HtmlBlog extends Omit<Blog, 'content'> {
  content: string; // HTML content instead of BlogContent[]
}
import { adminBlogApi, adminBlogUtils } from "@/lib/supabase-admin";
import React18QuillEditor from "@/components/admin/React18QuillEditor";
import ImageUpload from "@/components/ImageUpload";

// HTML content utility functions
const htmlUtils = {
  // Extract plain text from HTML
  extractPlainText(html: string): string {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  },

  // Count words in HTML content
  countWords(html: string): number {
    const plainText = this.extractPlainText(html);
    return plainText.trim() ? plainText.trim().split(/\s+/).length : 0;
  },

  // Generate excerpt from HTML content
  generateExcerpt(html: string, maxLength = 160): string {
    const plainText = this.extractPlainText(html);
    return plainText.length > maxLength
      ? plainText.substring(0, maxLength).trim() + '...'
      : plainText.trim();
  },

  // Extract first image from HTML content
  extractFirstImage(html: string): string | null {
    const div = document.createElement('div');
    div.innerHTML = html;
    const img = div.querySelector('img');
    return img ? img.src : null;
  },

  // Convert HTML to BlogContent format for database storage
  convertHtmlToBlogContent(html: string): BlogContent[] {
    if (!html.trim()) return [];

    // Simple approach: store HTML content as text in a paragraph
    // We'll create a custom renderer to handle this
    return [
      {
        type: 'paragraph',
        content: [
          {
            type: 'text',
            text: `__HTML_CONTENT__${html}__HTML_CONTENT__` // Wrap with markers to identify HTML content
          }
        ]
      }
    ];
  }
};

export default function NewBlogPage() {
  const router = useRouter();
  const [loading, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    featured_image: '',
    author_name: 'TraderBase Team',
    author_avatar: '',
    published: false,
    featured: false,
    tags: [] as string[],
    meta_title: '',
    meta_description: ''
  });
  const [content, setContent] = useState<string>('');
  const [tagInput, setTagInput] = useState('');
  const [errors, setErrors] = useState<string[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  const handleInputChange = (field: string, value: string | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-generate slug from title
    if (field === 'title' && typeof value === 'string' && !formData.slug) {
      setFormData(prev => ({
        ...prev,
        slug: adminBlogUtils.generateSlug(value)
      }));
    }

    // Auto-generate meta title from title
    if (field === 'title' && typeof value === 'string' && !formData.meta_title) {
      setFormData(prev => ({
        ...prev,
        meta_title: value
      }));
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: string[] = [];

    if (!formData.title.trim()) {
      newErrors.push('กรุณาใส่หัวข้อบทความ');
    }

    if (!formData.slug.trim()) {
      newErrors.push('กรุณาใส่ slug');
    }

    if (!content.trim() || content.trim() === '<p><br></p>') {
      newErrors.push('กรุณาเพิ่มเนื้อหาบทความ');
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleSave = async (publish = false) => {
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);

      // Auto-generate excerpt if not provided
      const excerpt = formData.excerpt || htmlUtils.generateExcerpt(content);

      // Calculate reading time
      const wordCount = htmlUtils.countWords(content);
      const reading_time = Math.ceil(wordCount / 200); // 200 words per minute

      // Auto-set featured image from content if not provided
      const featured_image = formData.featured_image || htmlUtils.extractFirstImage(content) || undefined;

      const blogData = {
        ...formData,
        content: htmlUtils.convertHtmlToBlogContent(content),
        excerpt,
        reading_time,
        featured_image,
        published: publish,
        published_at: publish ? new Date().toISOString() : undefined
      };

      const savedBlog = await adminBlogApi.createBlog(blogData);

      if (publish) {
        router.push(`/blogs/${encodeURIComponent(savedBlog.slug)}`);
      } else {
        router.push('/admin/blogs');
      }
    } catch (error) {
      console.error('Error saving blog:', error);
      setErrors(['เกิดข้อผิดพลาดในการบันทึกบทความ']);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex justify-between items-center"
      >
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">เขียนบทความใหม่</h1>
          <p className="text-gray-400">สร้างเนื้อหาใหม่สำหรับ TraderBase Blog</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="bordered"
            className="border-white/20 text-white hover:bg-white/10"
            onPress={() => router.back()}
          >
            ยกเลิก
          </Button>
          <Button
            onPress={() => setShowPreview(!showPreview)}
            className="bg-purple-600 text-white hover:bg-purple-700"
          >
            {showPreview ? 'แก้ไข' : 'ดูตัวอย่าง'}
          </Button>
          <Button
            onPress={() => handleSave(false)}
            disabled={loading}
            className="bg-gray-600 text-white hover:bg-gray-700"
          >
            {loading ? 'กำลังบันทึก...' : 'บันทึกร่าง'}
          </Button>
          <Button
            onPress={() => handleSave(true)}
            disabled={loading}
            className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold"
          >
            {loading ? 'กำลังเผยแพร่...' : 'เผยแพร่'}
          </Button>
        </div>
      </motion.div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="bg-red-500/10 border border-red-500/20">
            <CardBody className="p-4">
              <h3 className="text-red-400 font-semibold mb-2">กรุณาแก้ไขข้อผิดพลาดต่อไปนี้:</h3>
              <ul className="list-disc list-inside text-red-300 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Preview Mode */}
      {showPreview ? (
        <div className="min-h-screen bg-gradient-to-br from-black via-blue-900/20 to-black overflow-hidden">
          <div className="max-w-4xl mx-auto p-8">
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-white mb-4">{formData.title || 'ตัวอย่างหัวข้อ'}</h1>
              <p className="text-gray-400 mb-4">{formData.excerpt || htmlUtils.generateExcerpt(content)}</p>
              <div className="flex items-center gap-4 text-gray-400 text-sm">
                <span>{formData.author_name}</span>
                <span>{htmlUtils.countWords(content)} คำ</span>
                <span>{Math.ceil(htmlUtils.countWords(content) / 200)} นาทีในการอ่าน</span>
              </div>
            </div>
            <div className="bg-gray-900/30 border border-gray-700/30 rounded-lg p-8">
              <div
                className="prose prose-invert max-w-none text-gray-300"
                dangerouslySetInnerHTML={{ __html: content }}
              />
            </div>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="bg-black/20 border border-white/10">
                <CardHeader>
                  <h3 className="text-xl font-semibold text-white">ข้อมูลพื้นฐาน</h3>
                </CardHeader>
                <CardBody className="space-y-4">
                  <Input
                    label="หัวข้อบทความ"
                    placeholder="ใส่หัวข้อบทความ..."
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    classNames={{
                      input: "bg-black/20 text-white",
                      inputWrapper: "bg-black/20 border border-white/20"
                    }}
                  />

                  <Input
                    label="Slug (URL)"
                    placeholder="url-friendly-slug"
                    value={formData.slug}
                    onChange={(e) => handleInputChange('slug', e.target.value)}
                    classNames={{
                      input: "bg-black/20 text-white",
                      inputWrapper: "bg-black/20 border border-white/20"
                    }}
                  />

                  <Textarea
                    label="สรุปบทความ"
                    placeholder="เขียนสรุปสั้นๆ ของบทความ..."
                    value={formData.excerpt}
                    onChange={(e) => handleInputChange('excerpt', e.target.value)}
                    classNames={{
                      input: "bg-black/20 text-white",
                      inputWrapper: "bg-black/20 border border-white/20"
                    }}
                    minRows={3}
                  />

                  <div>
                    <label className="block text-white text-sm font-medium mb-2">
                      รูปภาพหน้าปก
                    </label>
                    <ImageUpload
                      currentImage={formData.featured_image}
                      onImageUpload={(imageUrl) => handleInputChange('featured_image', imageUrl)}
                    />
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            {/* Content Editor */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="bg-black/20 border border-white/10">
                <CardHeader>
                  <h3 className="text-xl font-semibold text-white">เนื้อหาบทความ</h3>
                </CardHeader>
                <CardBody>
                  <React18QuillEditor
                    value={content}
                    onChange={setContent}
                    placeholder="เริ่มเขียนเนื้อหาบทความ..."
                    height={500}
                  />
                </CardBody>
              </Card>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Publish Settings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="bg-black/20 border border-white/10">
                <CardHeader>
                  <h3 className="text-lg font-semibold text-white">การเผยแพร่</h3>
                </CardHeader>
                <CardBody className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-white">เผยแพร่ทันที</span>
                    <Switch
                      isSelected={formData.published}
                      onValueChange={(value) => handleInputChange('published', value)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-white">บทความแนะนำ</span>
                    <Switch
                      isSelected={formData.featured}
                      onValueChange={(value) => handleInputChange('featured', value)}
                    />
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            {/* Tags */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <Card className="bg-black/20 border border-white/10">
                <CardHeader>
                  <h3 className="text-lg font-semibold text-white">แท็ก</h3>
                </CardHeader>
                <CardBody className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      placeholder="เพิ่มแท็ก..."
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && addTag()}
                      classNames={{
                        input: "bg-black/20 text-white",
                        inputWrapper: "bg-black/20 border border-white/20"
                      }}
                    />
                    <Button
                      onPress={addTag}
                      className="bg-blue-500 text-white hover:bg-blue-600"
                    >
                      เพิ่ม
                    </Button>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag) => (
                      <Chip
                        key={tag}
                        onClose={() => removeTag(tag)}
                        className="bg-blue-500/20 text-blue-300"
                      >
                        {tag}
                      </Chip>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          </div>
        </div>
      )}
    </div>
  );
}
