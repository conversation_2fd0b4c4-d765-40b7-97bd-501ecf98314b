-- Create storage bucket for blog images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'blog-images',
    'blog-images',
    true,
    5242880, -- 5MB limit
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for storage

-- Allow public read access to blog images
CREATE POLICY "Public can view blog images" ON storage.objects
    FOR SELECT USING (bucket_id = 'blog-images');

-- Allow admin users to upload images
CREATE POLICY "Admin can upload blog images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'blog-images' AND
        auth.role() = 'authenticated' AND (
            (auth.jwt() ->> 'user_metadata')::json ->> 'role' = 'admin' OR
            (auth.jwt() ->> 'app_metadata')::json ->> 'role' = 'admin'
        )
    );

-- Allow admin users to update images
CREATE POLICY "Admin can update blog images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'blog-images' AND
        auth.role() = 'authenticated' AND (
            (auth.jwt() ->> 'user_metadata')::json ->> 'role' = 'admin' OR
            (auth.jwt() ->> 'app_metadata')::json ->> 'role' = 'admin'
        )
    );

-- Allow admin users to delete images
CREATE POLICY "Admin can delete blog images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'blog-images' AND
        auth.role() = 'authenticated' AND (
            (auth.jwt() ->> 'user_metadata')::json ->> 'role' = 'admin' OR
            (auth.jwt() ->> 'app_metadata')::json ->> 'role' = 'admin'
        )
    );
