'use client'

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import NavigationBar from "@/components/NavigationBar";
import FooterSection from "@/components/FooterSection";
import ForexBasicsSection from "@/components/ForexBasicsSection";
import TradingToolsSection from "@/components/TradingToolsSection";
import TradingSystemsSection from "@/components/TradingSystemsSection";
import { useReadStatus } from "@/hooks/useReadStatus";
import BrokerSelectionSection from "@/components/BrokerSelectionSection";

export default function ForexTradingStepsPage() {
  const [mounted, setMounted] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('step-1');
  const [isSticky, setIsSticky] = useState(false);
  const { getReadCount, isClient } = useReadStatus();



  useEffect(() => {
    setMounted(true);

    // Handle scroll for section detection and sticky navigation
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const heroHeight = 400; // Approximate height of hero section
      setIsSticky(scrollPosition > heroHeight);

      // Define the step sections for this page
      const stepSections = ['step-1', 'step-2', 'step-3', 'step-4'];

      const sectionElements = stepSections.map(sectionId => ({
        id: sectionId,
        element: document.getElementById(sectionId),
        offset: document.getElementById(sectionId)?.offsetTop || 0
      }));

      const currentSection = sectionElements.find((section, index) => {
        const nextSection = sectionElements[index + 1];
        return window.scrollY >= section.offset - 100 &&
          (!nextSection || window.scrollY < nextSection.offset - 100);
      });

      if (currentSection) {
        setActiveSection(currentSection.id);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  if (!mounted) return null;

  return (
    <div className="min-h-full bg-gradient-to-br from-black via-blue-900/20 to-black overflow-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden -z-10">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Navigation */}
      <NavigationBar
        activeSection={activeSection}
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
        scrollToSection={scrollToSection}
      />

      <div className="container mx-auto px-6 py-20 pt-32">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent !leading-normal mb-6">
            ขั้นตอนการเรียนรู้
            <br />
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Forex Trading
            </span>
          </h1>
          <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
            เรียนรู้การเทรด FOREX อย่างเป็นระบบ ตั้งแต่พื้นฐานไปจนถึงการเลือกโบรกเกอร์และเครื่องมือเทรด
          </p>
        </motion.div>

        {/* Step Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-16"
        >
          {[
            { step: 1, title: "พื้นฐาน Forex", active: true, id: "step-1", prefix: "step1", total: 10 },
            { step: 2, title: "เลือกโบรกเกอร์", active: true, id: "step-2", prefix: "step2", total: 10 },
            { step: 3, title: "เครื่องมือเทรด", active: true, id: "step-3", prefix: "step3", total: 11 },
            { step: 4, title: "การซื้อขาย", active: true, id: "step-4", prefix: "step4", total: 11 }
          ].map((item, index) => {
            const readCount = isClient ? getReadCount(item.prefix) : 0;
            return (
              <button
                key={index}
                onClick={() => scrollToSection(item.id)}
                className={`flex items-center gap-3 px-4 py-2 rounded-lg border transition-all duration-300 hover:scale-105 ${activeSection === item.id
                  ? 'bg-blue-500/30 border-blue-400/70 text-blue-200 shadow-lg shadow-blue-500/20'
                  : item.active
                    ? 'bg-blue-500/20 border-blue-500/50 text-blue-300 hover:bg-blue-500/25'
                    : 'bg-gray-800/30 border-gray-700/50 text-gray-500 hover:bg-gray-700/30'
                  }`}
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${activeSection === item.id
                  ? 'bg-blue-400 text-white shadow-lg'
                  : item.active
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-700 text-gray-400'
                  }`}>
                  {item.step}
                </div>
                <div className="flex flex-col">
                  <span className="font-medium">{item.title}</span>
                  <span className="text-xs text-gray-400">
                    {readCount}/{item.total} อ่านแล้ว
                  </span>
                </div>
              </button>
            );
          })}
        </motion.div>

        {/* Sticky Step Navigation */}
        <div className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isSticky ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
          }`}>
          <div className="bg-black/90 backdrop-blur-md border-b border-gray-800/50">
            <div className="container mx-auto px-6 py-4">
              <div className="flex flex-wrap justify-center gap-2">
                {[
                  { step: 1, title: "พื้นฐาน Forex", id: "step-1", prefix: "step1", total: 10 },
                  { step: 2, title: "เลือกโบรกเกอร์", id: "step-2", prefix: "step2", total: 10 },
                  { step: 3, title: "เครื่องมือเทรด", id: "step-3", prefix: "step3", total: 11 },
                  { step: 4, title: "การซื้อขาย", id: "step-4", prefix: "step4", total: 11 }
                ].map((item, index) => {
                  const readCount = isClient ? getReadCount(item.prefix) : 0;
                  return (
                    <button
                      key={index}
                      onClick={() => scrollToSection(item.id)}
                      className={`flex items-center gap-2 px-3 py-2 rounded-lg border text-sm transition-all duration-300 hover:scale-105 ${activeSection === item.id
                        ? 'bg-blue-500/30 border-blue-400/70 text-blue-200 shadow-lg shadow-blue-500/20'
                        : 'bg-blue-500/10 border-blue-500/30 text-blue-300 hover:bg-blue-500/20'
                        }`}
                    >
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${activeSection === item.id
                        ? 'bg-blue-400 text-white shadow-lg'
                        : 'bg-blue-500 text-white'
                        }`}>
                        {item.step}
                      </div>
                      <div className="hidden sm:flex flex-col">
                        <span className="font-medium">{item.title}</span>
                        <span className="text-xs text-gray-400">
                          {readCount}/{item.total}
                        </span>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Trading Steps Sections */}
        <div className="space-y-20">
          {/* Step 1: Forex Basics */}
          <section id="step-1">
            <ForexBasicsSection />
          </section>

          {/* Step 2: Broker Selection */}
          <section id="step-2">
            <BrokerSelectionSection />
          </section>

          {/* Step 3: Trading Tools */}
          <section id="step-3">
            <TradingToolsSection />
          </section>

          {/* Step 4: Trading Systems */}
          <section id="step-4">
            <TradingSystemsSection />
          </section>
        </div>

        {/* Footer */}
        <FooterSection />
      </div>
    </div>
  );
}
