'use client'

import { motion } from 'framer-motion'
import { <PERSON>, CardBody, Chip } from '@heroui/react'
import Image from 'next/image'
import { BlogContent } from '@/lib/supabase'
import BlogContentRenderer from '@/components/BlogContentRenderer'

interface BlogPreviewProps {
  title: string
  excerpt: string
  featuredImage: string
  authorName: string
  authorAvatar: string
  tags: string[]
  content: BlogContent[]
  readingTime: number
}

export default function BlogPreview({
  title,
  excerpt,
  featuredImage,
  authorName,
  authorAvatar,
  tags,
  content,
  readingTime
}: BlogPreviewProps) {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('th-TH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-blue-900/20 to-black overflow-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden -z-10">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Preview Header */}
      <div className="relative pt-8 pb-4 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">👁️</span>
              </div>
              <div>
                <h1 className="text-white font-semibold">ตัวอย่างบทความ</h1>
                <p className="text-gray-400 text-sm">นี่คือตัวอย่างการแสดงผลบทความ</p>
              </div>
            </div>
            <div className="px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full">
              <span className="text-blue-300 text-sm font-medium">Preview Mode</span>
            </div>
          </div>
        </div>
      </div>

      {/* Blog Header */}
      <section className="relative pt-16 pb-16 px-4 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute top-40 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-1/2 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Breadcrumb */}
            <div className="flex items-center gap-2 text-sm text-gray-500 mb-6">
              <span>บทความ</span>
              <span>/</span>
              <span className="text-gray-300">{title || 'ตัวอย่างบทความ'}</span>
            </div>

            {/* Tags */}
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-6">
                {tags.map((tag) => (
                  <Chip
                    key={tag}
                    className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-300 border border-blue-500/20"
                  >
                    {tag}
                  </Chip>
                ))}
              </div>
            )}

            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              {title || 'ตัวอย่างหัวข้อบทความ'}
            </h1>

            {/* Excerpt */}
            {excerpt && (
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                {excerpt}
              </p>
            )}

            {/* Meta Info */}
            <div className="flex flex-wrap items-center gap-6 text-gray-400 mb-8">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                  {authorAvatar ? (
                    <Image src={authorAvatar} alt={authorName} width={40} height={40} className="w-10 h-10 rounded-full" />
                  ) : (
                    <span className="text-white font-semibold text-sm">
                      {authorName.charAt(0)}
                    </span>
                  )}
                </div>
                <div>
                  <p className="text-white font-medium">{authorName}</p>
                  <p className="text-sm">{formatDate(new Date())}</p>
                </div>
              </div>
              {readingTime > 0 && (
                <div className="flex items-center gap-2">
                  <span>⏱️</span>
                  <span>{readingTime} นาทีในการอ่าน</span>
                </div>
              )}
            </div>

            {/* Featured Image */}
            {featuredImage && (
              <div className="mb-12">
                <Image
                  src={featuredImage}
                  alt={title}
                  width={800}
                  height={400}
                  className="w-full h-64 md:h-96 object-cover rounded-2xl shadow-2xl"
                />
              </div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Blog Content */}
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <Card className="bg-gray-900/30 border border-gray-700/30 backdrop-blur-sm">
              <CardBody className="p-8 md:p-12">
                {content.length > 0 ? (
                  <BlogContentRenderer content={content} />
                ) : (
                  <div className="text-center py-16">
                    <div className="text-6xl mb-4">📝</div>
                    <h3 className="text-2xl font-semibold text-white mb-2">ยังไม่มีเนื้อหา</h3>
                    <p className="text-gray-400">เพิ่มเนื้อหาบทความเพื่อดูตัวอย่าง</p>
                  </div>
                )}
              </CardBody>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Preview Footer */}
      <div className="relative py-8 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-gray-900/50 border border-gray-700/30 rounded-full backdrop-blur-sm">
            <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></span>
            <span className="text-gray-300 text-sm">นี่คือโหมดตัวอย่าง - บทความจะแสดงผลแบบนี้เมื่อเผยแพร่</span>
          </div>
        </div>
      </div>
    </div>
  )
}
