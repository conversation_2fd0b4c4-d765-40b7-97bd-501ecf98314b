'use client'

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardBody,
  Button,
  Input,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure
} from "@heroui/react";
import { Blog } from "@/lib/supabase";
import { adminBlogApi } from "@/lib/supabase-admin";
import Link from "next/link";
import Image from "next/image";

export default function AdminBlogsPage() {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'published' | 'draft'>('all');
  const [selectedBlog, setSelectedBlog] = useState<Blog | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const { isOpen, onOpen, onClose } = useDisclosure();

  const loadBlogs = useCallback(async () => {
    try {
      setLoading(true);

      let data;
      if (searchQuery) {
        data = await adminBlogApi.searchBlogs(searchQuery, page, 10);
      } else {
        data = await adminBlogApi.getAllBlogs(page, 10, selectedStatus);
      }

      if (page === 1) {
        setBlogs(data.blogs);
      } else {
        setBlogs(prev => [...prev, ...data.blogs]);
      }
      setHasMore(data.hasMore);
    } catch (error) {
      console.error('Error loading blogs:', error);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, selectedStatus, page]);

  useEffect(() => {
    loadBlogs();
  }, [loadBlogs]);

  const handleDelete = async (blog: Blog) => {
    setSelectedBlog(blog);
    onOpen();
  };

  const confirmDelete = async () => {
    if (!selectedBlog) return;

    try {
      await adminBlogApi.deleteBlog(selectedBlog.id);
      setBlogs(prev => prev.filter(blog => blog.id !== selectedBlog.id));
      onClose();
      setSelectedBlog(null);
    } catch (error) {
      console.error('Error deleting blog:', error);
      alert('เกิดข้อผิดพลาดในการลบบทความ');
    }
  };

  const togglePublished = async (blog: Blog) => {
    try {
      const updatedBlog = await adminBlogApi.updateBlog(blog.id, {
        published: !blog.published,
        published_at: !blog.published ? new Date().toISOString() : undefined
      });

      setBlogs(prev => prev.map(b => b.id === blog.id ? updatedBlog : b));
    } catch (error) {
      console.error('Error updating blog:', error);
      alert('เกิดข้อผิดพลาดในการอัปเดตบทความ');
    }
  };

  const toggleFeatured = async (blog: Blog) => {
    try {
      const updatedBlog = await adminBlogApi.updateBlog(blog.id, {
        featured: !blog.featured
      });

      setBlogs(prev => prev.map(b => b.id === blog.id ? updatedBlog : b));
    } catch (error) {
      console.error('Error updating blog:', error);
      alert('เกิดข้อผิดพลาดในการอัปเดตบทความ');
    }
  };

  const loadMore = () => {
    setPage(prev => prev + 1);
  };

  const resetFilters = () => {
    setSearchQuery('');
    setSelectedStatus('all');
    setPage(1);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const statusOptions = [
    { key: 'all', label: 'ทั้งหมด' },
    { key: 'published', label: 'เผยแพร่แล้ว' },
    { key: 'draft', label: 'ร่าง' }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6"
      >
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-3">
            จัดการบทความ
          </h1>
          <p className="text-gray-300 text-lg">จัดการและแก้ไขบทความทั้งหมดอย่างง่ายดาย</p>
        </div>
        <Link href="/admin/blogs/new">
          <Button className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold px-8 py-4 text-lg rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
            เขียนบทความใหม่
          </Button>
        </Link>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <Card className="bg-black/30 backdrop-blur-sm border border-blue-500/20 shadow-lg shadow-blue-500/10">
          <CardBody className="p-8">
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex-1">
                <Input
                  placeholder="ค้นหาบทความ..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  classNames={{
                    input: "bg-black/30 text-white text-lg",
                    inputWrapper: "bg-black/30 border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 backdrop-blur-sm",
                    label: "text-gray-300 font-semibold"
                  }}
                />
              </div>
              <div className="flex gap-3">
                {statusOptions.map((option) => (
                  <Button
                    key={option.key}
                    onPress={() => setSelectedStatus(option.key as 'all' | 'published' | 'draft')}
                    variant={selectedStatus === option.key ? "solid" : "bordered"}
                    className={
                      selectedStatus === option.key
                        ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold shadow-lg"
                        : "border-blue-500/30 text-blue-300 hover:bg-blue-500/10 hover:border-blue-500/50 transition-all duration-300 font-semibold"
                    }
                  >
                    {option.label}
                  </Button>
                ))}
                {(searchQuery || selectedStatus !== 'all') && (
                  <Button
                    onPress={resetFilters}
                    variant="ghost"
                    className="text-gray-400 hover:text-white hover:bg-gray-500/10 transition-all duration-300 font-semibold"
                  >
                    ล้างตัวกรอง
                  </Button>
                )}
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Blogs List */}
      {loading && blogs.length === 0 ? (
        <div className="text-center text-white py-12">กำลังโหลด...</div>
      ) : blogs.length === 0 ? (
        <Card className="bg-black/20 border border-white/10">
          <CardBody className="p-12 text-center">
            <div className="text-6xl mb-4 text-gray-400">▣</div>
            <h3 className="text-xl font-semibold text-white mb-2">ไม่พบบทความ</h3>
            <p className="text-gray-400 mb-6">เริ่มต้นสร้างบทความแรกของคุณ</p>
            <Link href="/admin/blogs/new">
              <Button className="bg-gradient-to-r from-blue-500 to-green-500 text-white font-semibold">
                เขียนบทความใหม่
              </Button>
            </Link>
          </CardBody>
        </Card>
      ) : (
        <div className="space-y-4">
          {blogs.map((blog, index) => (
            <motion.div
              key={blog.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="bg-black/20 border border-white/10 hover:border-white/20 transition-all">
                <CardBody className="p-6">
                  <div className="flex flex-col md:flex-row gap-4">
                    {/* Featured Image */}
                    {blog.featured_image && (
                      <div className="w-full md:w-32 h-32 relative rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={blog.featured_image}
                          alt={blog.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}

                    {/* Content */}
                    <div className="flex-1">
                      <div className="flex flex-wrap gap-2 mb-3">
                        <Chip
                          className={`text-xs ${blog.published
                            ? 'bg-green-500/20 text-green-300'
                            : 'bg-yellow-500/20 text-yellow-300'
                            }`}
                        >
                          {blog.published ? 'เผยแพร่' : 'ร่าง'}
                        </Chip>
                        {blog.featured && (
                          <Chip className="bg-orange-500/20 text-orange-300 text-xs">
                            แนะนำ
                          </Chip>
                        )}
                        {blog.tags.slice(0, 3).map((tag) => (
                          <Chip
                            key={tag}
                            className="bg-blue-500/20 text-blue-300 text-xs"
                          >
                            {tag}
                          </Chip>
                        ))}
                      </div>

                      <h3 className="text-xl font-semibold text-white mb-2 line-clamp-2">
                        {blog.title}
                      </h3>

                      <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                        {blog.excerpt}
                      </p>

                      <div className="flex items-center gap-4 text-xs text-gray-400 mb-4">
                        <span>สร้าง: {formatDate(blog.created_at)}</span>
                        {blog.published_at && (
                          <span>เผยแพร่: {formatDate(blog.published_at)}</span>
                        )}
                        <span>{blog.view_count} views</span>
                        {blog.reading_time && (
                          <span>{blog.reading_time} นาที</span>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex flex-wrap gap-2">
                        <Link href={`/admin/blogs/edit/${blog.id}`}>
                          <Button size="sm" className="bg-blue-500 text-white hover:bg-blue-600">
                            แก้ไข
                          </Button>
                        </Link>

                        <Button
                          size="sm"
                          onPress={() => togglePublished(blog)}
                          className={
                            blog.published
                              ? "bg-yellow-500 text-white hover:bg-yellow-600"
                              : "bg-green-500 text-white hover:bg-green-600"
                          }
                        >
                          {blog.published ? 'ยกเลิกเผยแพร่' : 'เผยแพร่'}
                        </Button>

                        <Button
                          size="sm"
                          onPress={() => toggleFeatured(blog)}
                          variant={blog.featured ? "solid" : "bordered"}
                          className={
                            blog.featured
                              ? "bg-orange-500 text-white hover:bg-orange-600"
                              : "border-orange-500 text-orange-400 hover:bg-orange-500/10"
                          }
                        >
                          {blog.featured ? 'ยกเลิกแนะนำ' : 'ตั้งเป็นแนะนำ'}
                        </Button>

                        <Link href={`/blogs/${encodeURIComponent(blog.slug)}`} target="_blank">
                          <Button size="sm" variant="bordered" className="border-white/20 text-white hover:bg-white/10">
                            ดู
                          </Button>
                        </Link>

                        <Button
                          size="sm"
                          onPress={() => handleDelete(blog)}
                          className="bg-red-500 text-white hover:bg-red-600"
                        >
                          ลบ
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}

          {/* Load More */}
          {hasMore && (
            <div className="text-center pt-6">
              <Button
                onPress={loadMore}
                disabled={loading}
                className="bg-gradient-to-r from-blue-500 to-green-500 text-white font-semibold"
              >
                {loading ? 'กำลังโหลด...' : 'โหลดเพิ่มเติม'}
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          <ModalHeader>
            <h3 className="text-lg font-semibold">ยืนยันการลบ</h3>
          </ModalHeader>
          <ModalBody>
            <p>คุณแน่ใจหรือไม่ที่จะลบบทความ &ldquo;{selectedBlog?.title}&rdquo;?</p>
            <p className="text-red-500 text-sm">การดำเนินการนี้ไม่สามารถย้อนกลับได้</p>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" onPress={onClose}>
              ยกเลิก
            </Button>
            <Button className="bg-red-500 text-white" onPress={confirmDelete}>
              ลบ
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}
