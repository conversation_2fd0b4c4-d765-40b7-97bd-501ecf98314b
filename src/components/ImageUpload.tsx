'use client'

import { useState, useRef, useCallback } from 'react'
import { <PERSON><PERSON>, <PERSON>, CardBody } from '@heroui/react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { SupabaseStorage } from '@/lib/supabase-storage'

interface ImageUploadProps {
  onImageUpload: (imageUrl: string) => void
  currentImage?: string
  className?: string
}

export default function ImageUpload({ onImageUpload, currentImage, className = '' }: ImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = useCallback(async (file: File) => {
    setIsUploading(true)
    setUploadProgress(0)
    setUploadError(null)

    try {
      // Check upload permission
      const hasPermission = await SupabaseStorage.checkUploadPermission()
      if (!hasPermission) {
        throw new Error('คุณไม่มีสิทธิ์ในการอัปโหลดรูปภาพ')
      }

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 80) {
            clearInterval(progressInterval)
            return 80
          }
          return prev + 20
        })
      }, 200)

      // Upload to Supabase Storage
      const result = await SupabaseStorage.uploadImage(file, 'uploads')

      clearInterval(progressInterval)

      if (!result.success) {
        throw new Error(result.error || 'เกิดข้อผิดพลาดในการอัปโหลด')
      }

      setUploadProgress(100)
      onImageUpload(result.url!)

      setTimeout(() => {
        setIsUploading(false)
        setUploadProgress(0)
      }, 500)

    } catch (error) {
      console.error('Error uploading image:', error)
      setUploadError(error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการอัปโหลด')
      setIsUploading(false)
      setUploadProgress(0)
    }
  }, [onImageUpload])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files)
    const imageFile = files.find(file => file.type.startsWith('image/'))

    if (imageFile) {
      handleFileUpload(imageFile)
    }
  }, [handleFileUpload])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && file.type.startsWith('image/')) {
      handleFileUpload(file)
    }
  }, [handleFileUpload])

  const removeImage = async () => {
    if (currentImage) {
      // Try to delete from Supabase Storage if it's a Supabase URL
      if (currentImage.includes('supabase')) {
        try {
          await SupabaseStorage.deleteImage(currentImage)
        } catch (error) {
          console.error('Error deleting image:', error)
        }
      }
    }

    onImageUpload('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    setUploadError(null)
  }

  return (
    <div className={className}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Error Message */}
      {uploadError && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg"
        >
          <p className="text-red-400 text-sm">{uploadError}</p>
        </motion.div>
      )}

      <AnimatePresence mode="wait">
        {currentImage ? (
          <motion.div
            key="image-preview"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="bg-gray-900/30 border border-gray-700/30 backdrop-blur-sm">
              <CardBody className="p-4">
                <div className="relative group">
                  <Image
                    src={currentImage}
                    alt="Uploaded image"
                    width={400}
                    height={192}
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-center justify-center">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onPress={() => fileInputRef.current?.click()}
                        className="bg-blue-500 text-white hover:bg-blue-600"
                      >
                        เปลี่ยน
                      </Button>
                      <Button
                        size="sm"
                        onPress={removeImage}
                        className="bg-red-500 text-white hover:bg-red-600"
                      >
                        ลบ
                      </Button>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ) : (
          <motion.div
            key="upload-area"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <Card
              className={`bg-gray-900/30 border-2 border-dashed transition-all duration-300 backdrop-blur-sm cursor-pointer ${isDragging
                ? 'border-blue-500 bg-blue-500/10'
                : 'border-gray-700/50 hover:border-blue-500/50'
                }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <CardBody className="p-8">
                <div className="text-center">
                  {isUploading ? (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="space-y-4"
                    >
                      <div className="text-4xl">📤</div>
                      <div className="space-y-2">
                        <p className="text-white font-medium">กำลังอัปโหลด...</p>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <motion.div
                            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${uploadProgress}%` }}
                            transition={{ duration: 0.3 }}
                          />
                        </div>
                        <p className="text-gray-400 text-sm">{uploadProgress}%</p>
                      </div>
                    </motion.div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="space-y-4"
                    >
                      <div className="text-6xl">🖼️</div>
                      <div className="space-y-2">
                        <h3 className="text-white font-semibold text-lg">
                          อัปโหลดรูปภาพ
                        </h3>
                        <p className="text-gray-400">
                          ลากและวางรูปภาพที่นี่ หรือคลิกเพื่อเลือกไฟล์
                        </p>
                        <p className="text-gray-500 text-sm">
                          รองรับ: JPG, PNG, GIF, WebP
                        </p>
                      </div>
                      <Button
                        className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold"
                        onPress={() => {
                          fileInputRef.current?.click()
                        }}
                      >
                        เลือกรูปภาพ
                      </Button>
                    </motion.div>
                  )}
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
