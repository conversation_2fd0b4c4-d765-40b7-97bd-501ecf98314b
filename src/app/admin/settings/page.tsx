'use client'

import { motion } from "framer-motion";
import { <PERSON>, CardBody, CardHeader, Button, Input, Textarea } from "@heroui/react";

export default function AdminSettingsPage() {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h1 className="text-3xl font-bold text-white mb-2">ตั้งค่า</h1>
        <p className="text-gray-400">จัดการการตั้งค่าระบบและเว็บไซต์</p>
      </motion.div>

      {/* Site Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <Card className="bg-black/20 border border-white/10">
          <CardHeader>
            <h3 className="text-xl font-semibold text-white">ตั้งค่าเว็บไซต์</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <Input
              label="ชื่อเว็บไซต์"
              placeholder="TraderBase"
              classNames={{
                input: "bg-black/20 text-white",
                inputWrapper: "bg-black/20 border border-white/20"
              }}
            />
            
            <Textarea
              label="คำอธิบายเว็บไซต์"
              placeholder="เว็บไซต์สำหรับเรียนรู้การเทรด Forex"
              classNames={{
                input: "bg-black/20 text-white",
                inputWrapper: "bg-black/20 border border-white/20"
              }}
              minRows={3}
            />

            <Input
              label="URL เว็บไซต์"
              placeholder="https://traderbase.com"
              classNames={{
                input: "bg-black/20 text-white",
                inputWrapper: "bg-black/20 border border-white/20"
              }}
            />

            <div className="flex justify-end">
              <Button className="bg-blue-500 text-white hover:bg-blue-600">
                บันทึกการตั้งค่า
              </Button>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Blog Settings */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card className="bg-black/20 border border-white/10">
          <CardHeader>
            <h3 className="text-xl font-semibold text-white">ตั้งค่าบล็อก</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <Input
              label="จำนวนบทความต่อหน้า"
              type="number"
              placeholder="10"
              classNames={{
                input: "bg-black/20 text-white",
                inputWrapper: "bg-black/20 border border-white/20"
              }}
            />

            <Input
              label="ชื่อผู้เขียนเริ่มต้น"
              placeholder="TraderBase Team"
              classNames={{
                input: "bg-black/20 text-white",
                inputWrapper: "bg-black/20 border border-white/20"
              }}
            />

            <div className="flex justify-end">
              <Button className="bg-blue-500 text-white hover:bg-blue-600">
                บันทึกการตั้งค่า
              </Button>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Database Info */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card className="bg-black/20 border border-white/10">
          <CardHeader>
            <h3 className="text-xl font-semibold text-white">ข้อมูลระบบ</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-white/5 rounded-lg">
                <h4 className="text-white font-semibold mb-2">เวอร์ชันระบบ</h4>
                <p className="text-gray-300">v1.0.0</p>
              </div>
              
              <div className="p-4 bg-white/5 rounded-lg">
                <h4 className="text-white font-semibold mb-2">ฐานข้อมูล</h4>
                <p className="text-gray-300">Supabase PostgreSQL</p>
              </div>
              
              <div className="p-4 bg-white/5 rounded-lg">
                <h4 className="text-white font-semibold mb-2">สถานะ</h4>
                <p className="text-green-400">ออนไลน์</p>
              </div>
              
              <div className="p-4 bg-white/5 rounded-lg">
                <h4 className="text-white font-semibold mb-2">อัปเดตล่าสุด</h4>
                <p className="text-gray-300">{new Date().toLocaleDateString('th-TH')}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Backup & Maintenance */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card className="bg-black/20 border border-white/10">
          <CardHeader>
            <h3 className="text-xl font-semibold text-white">การบำรุงรักษา</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex flex-col md:flex-row gap-4">
              <Button
                className="bg-green-500 text-white hover:bg-green-600"
                disabled
              >
                สำรองข้อมูล (เร็วๆ นี้)
              </Button>
              
              <Button
                className="bg-orange-500 text-white hover:bg-orange-600"
                disabled
              >
                ล้างแคช (เร็วๆ นี้)
              </Button>
              
              <Button
                className="bg-purple-500 text-white hover:bg-purple-600"
                disabled
              >
                ตรวจสอบระบบ (เร็วๆ นี้)
              </Button>
            </div>
            
            <p className="text-gray-400 text-sm">
              ฟีเจอร์เหล่านี้จะพร้อมใช้งานในเวอร์ชันถัดไป
            </p>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
}
