-- Migration: Update slug generation function to support Thai characters
-- Created: 2024-12-24
-- Description: Updates the generate_slug function to properly handle Thai characters in blog slugs

-- Drop the existing function
DROP FUNCTION IF EXISTS generate_slug(TEXT);

-- Create updated function that supports Thai characters
CREATE OR REPLACE FUNCTION generate_slug(title TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN lower(
        trim(
            regexp_replace(
                regexp_replace(
                    regexp_replace(
                        -- Keep Thai characters (U+0E00-U+0E7F), English letters, numbers, spaces, and hyphens
                        regexp_replace(title, '[^\u0E00-\u0E7F\w\s-]', '', 'g'),
                        '\s+', '-', 'g'  -- Replace spaces with hyphens
                    ),
                    '-+', '-', 'g'  -- Replace multiple hyphens with single
                ),
                '^-+|-+$', '', 'g'  -- Remove leading/trailing hyphens
            )
        )
    );
END;
$$ LANGUAGE plpgsql;

-- Test the function with Thai characters
DO $$
DECLARE
    test_result TEXT;
BEGIN
    -- Test with Thai title
    test_result := generate_slug('สรุปทุกเรื่อง Forex Trading');
    RAISE NOTICE 'Thai slug test: %', test_result;
    
    -- Test with mixed Thai and English
    test_result := generate_slug('เริ่มต้นเทรด Forex สำหรับมือใหม่');
    RAISE NOTICE 'Mixed slug test: %', test_result;
    
    -- Test with special characters
    test_result := generate_slug('การเทรด!@# Forex $%^ ที่ดีที่สุด');
    RAISE NOTICE 'Special chars test: %', test_result;
END $$;

-- Update existing blog slugs that might have been affected
-- (Optional: only run if you have existing blogs with Thai titles that need fixing)
-- UPDATE blogs 
-- SET slug = generate_slug(title) 
-- WHERE slug ~ '^[a-z0-9-]+$' 
--   AND title ~ '[\u0E00-\u0E7F]'
--   AND slug NOT LIKE '%' || lower(regexp_replace(title, '[^\u0E00-\u0E7F\w]', '', 'g')) || '%';

-- Verify the trigger still works
-- The existing trigger should automatically use the updated function
