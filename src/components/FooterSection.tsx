'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "@heroui/react";

export default function FooterSection() {
  return (
    <footer id="contact" className="relative z-10 border-t border-blue-500/20 bg-black/50 backdrop-blur-md">
      <div className="container mx-auto px-6 py-12">
        <div className="grid md:grid-cols-4 gap-8 mb-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">T</span>
              </div>
              <span className="text-white font-bold text-xl">TraderBase</span>
            </div>
            <p className="text-gray-400 text-sm">
              แหล่งเรียนรู้การเทรด FOREX ที่ดีที่สุดในประเทศไทย พร้อมคอร์สฟรี รีวิวโบรกเกอร์ และระบบเทรดมืออาชีพ
            </p>
            <div className="flex gap-4">
              <Button variant="light" size="sm" className="text-gray-400 hover:text-white p-2 min-w-0">
                FB
              </Button>
              <Button variant="light" size="sm" className="text-gray-400 hover:text-white p-2 min-w-0">
                IG
              </Button>
              <Button variant="light" size="sm" className="text-gray-400 hover:text-white p-2 min-w-0">
                TW
              </Button>
              <Button variant="light" size="sm" className="text-gray-400 hover:text-white p-2 min-w-0">
                YT
              </Button>
            </div>
          </div>

          {/* Learning */}
          <div className="space-y-4">
            <h4 className="text-white font-semibold">การเรียนรู้</h4>
            <div className="space-y-2">
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#learn">
                คอร์สเรียน FOREX ฟรี
              </Link>
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#knowledge">
                คลังคำศัพท์ FOREX
              </Link>
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#knowledge">
                กลยุทธ์การเทรด
              </Link>
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#knowledge">
                การบริหารความเสี่ยง
              </Link>
            </div>
          </div>

          {/* Tools */}
          <div className="space-y-4">
            <h4 className="text-white font-semibold">เครื่องมือ</h4>
            <div className="space-y-2">
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#brokers">
                รีวิวโบรกเกอร์
              </Link>
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#systems">
                ระบบเทรดฟรี
              </Link>
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#systems">
                อินดิเคเตอร์
              </Link>
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#knowledge">
                คำนวณขนาดโพซิชั่น
              </Link>
            </div>
          </div>

          {/* About */}
          <div className="space-y-4">
            <h4 className="text-white font-semibold">เกี่ยวกับเรา</h4>
            <div className="space-y-2">
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#about">
                เกี่ยวกับองค์กร
              </Link>
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#about">
                ทีมงาน
              </Link>
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#contact">
                ติดต่อเรา
              </Link>
              <Link className="block text-gray-400 hover:text-white text-sm transition-colors" href="#privacy">
                นโยบายความเป็นส่วนตัว
              </Link>
            </div>
          </div>
        </div>

        <Divider className="bg-gray-700 mb-8" />

        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-gray-400 text-sm">
            © 2025 TraderBase. สงวนลิขสิทธิ์ทั้งหมด
          </p>
          <div className="flex gap-6">
            <Link className="text-gray-400 hover:text-white text-sm transition-colors" href="#terms">
              ข้อกำหนดการใช้งาน
            </Link>
            <Link className="text-gray-400 hover:text-white text-sm transition-colors" href="#privacy">
              นโยบายความเป็นส่วนตัว
            </Link>
            <Link className="text-gray-400 hover:text-white text-sm transition-colors" href="#disclaimer">
              คำเตือนความเสี่ยง
            </Link>
          </div>
        </div>

        {/* Risk Warning */}
        <div className="mt-8 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
          <p className="text-yellow-300 text-xs leading-relaxed">
            <strong>คำเตือนความเสี่ยง:</strong> การเทรด FOREX มีความเสี่ยงสูง และอาจไม่เหมาะสำหรับนักลงทุนทุกคน
            ความเป็นไปได้ที่จะสูญเสียเงินลงทุนบางส่วนหรือทั้งหมดมีอยู่ ดังนั้นคุณไม่ควรลงทุนด้วยเงินที่คุณไม่สามารถสูญเสียได้
            คุณควรตระหนักถึงความเสี่ยงทั้งหมดที่เกี่ยวข้องกับการเทรด FOREX และขอคำแนะนำจากที่ปรึกษาทางการเงินอิสระหากคุณมีข้อสงสัยใดๆ
          </p>
        </div>
      </div>
    </footer>
  );
}
