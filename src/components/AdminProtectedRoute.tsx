'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Card, CardBody, Button } from '@heroui/react'
import { useAuth } from '@/contexts/AuthContext'

interface AdminProtectedRouteProps {
  children: React.ReactNode
}

export default function AdminProtectedRoute({ children }: AdminProtectedRouteProps) {
  const { user, loading, isAdmin, signOut } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // Not authenticated, redirect to login
        router.push('/admin/login')
      } else if (!isAdmin) {
        // Authenticated but not admin, redirect to unauthorized page
        router.push('/admin/unauthorized')
      }
    }
  }, [user, loading, isAdmin, router])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mx-auto mb-4">
            <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
          <p className="text-white text-xl">กำลังตรวจสอบสิทธิ์...</p>
        </motion.div>
      </div>
    )
  }

  // Show unauthorized message for non-admin users
  if (user && !isAdmin) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-md"
        >
          <Card className="bg-black/20 border border-white/10 backdrop-blur-sm">
            <CardBody className="text-center p-8">
              <div className="text-6xl mb-4">🚫</div>
              <h1 className="text-2xl font-bold text-white mb-2">
                ไม่มีสิทธิ์เข้าถึง
              </h1>
              <p className="text-gray-400 mb-6">
                คุณไม่มีสิทธิ์เข้าถึงหน้าผู้ดูแลระบบ
              </p>
              
              <div className="space-y-3">
                <Button
                  onClick={() => signOut()}
                  className="w-full bg-red-500 text-white hover:bg-red-600"
                >
                  ออกจากระบบ
                </Button>
                
                <Button
                  variant="bordered"
                  className="w-full border-white/20 text-white hover:bg-white/10"
                  onClick={() => router.push('/')}
                >
                  กลับไปเว็บไซต์หลัก
                </Button>
              </div>

              <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <p className="text-yellow-400 text-sm">
                  <strong>หมายเหตุ:</strong> หากคุณควรมีสิทธิ์เข้าถึง กรุณาติดต่อผู้ดูแลระบบ
                </p>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>
    )
  }

  // Show login redirect message
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-2xl">🔐</span>
          </div>
          <p className="text-white text-xl">กำลังเปลี่ยนเส้นทางไปหน้าเข้าสู่ระบบ...</p>
        </motion.div>
      </div>
    )
  }

  // User is authenticated and is admin, render children
  return <>{children}</>
}
