'use client'

import { useState } from "react";
import { Button, Input, Textarea, Card, CardBody } from "@heroui/react";
import { BlogContent, BlogContentText } from "@/lib/supabase";
import { SupabaseStorage } from "@/lib/supabase-storage";

interface BlogEditorProps {
  initialContent?: BlogContent[];
  onChange: (content: BlogContent[]) => void;
}

export default function BlogEditor({ initialContent = [], onChange }: BlogEditorProps) {
  const [content, setContent] = useState<BlogContent[]>(initialContent);
  const [selectedBlockIndex, setSelectedBlockIndex] = useState<number | null>(null);
  const [uploadingBlockIndex, setUploadingBlockIndex] = useState<number | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const updateContent = (newContent: BlogContent[]) => {
    setContent(newContent);
    onChange(newContent);
  };

  const addBlock = (type: BlogContent['type'], index?: number) => {
    const newBlock: BlogContent = {
      type,
      content: type === 'image' ? undefined : [{ type: 'text', text: '' }],
      attrs: type === 'heading' ? { level: 2 } : type === 'image' ? { src: '', alt: '' } : undefined
    };

    const insertIndex = index !== undefined ? index + 1 : content.length;
    const newContent = [...content];
    newContent.splice(insertIndex, 0, newBlock);
    updateContent(newContent);
    setSelectedBlockIndex(insertIndex);
  };

  const updateBlock = (index: number, updates: Partial<BlogContent>) => {
    const newContent = [...content];
    newContent[index] = { ...newContent[index], ...updates };
    updateContent(newContent);
  };

  const deleteBlock = (index: number) => {
    const newContent = content.filter((_, i) => i !== index);
    updateContent(newContent);
    setSelectedBlockIndex(null);
  };

  const moveBlock = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === content.length - 1)
    ) {
      return;
    }

    const newContent = [...content];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    [newContent[index], newContent[targetIndex]] = [newContent[targetIndex], newContent[index]];
    updateContent(newContent);
    setSelectedBlockIndex(targetIndex);
  };

  const updateTextContent = (blockIndex: number, text: string) => {
    const textContent: BlogContentText[] = [{ type: 'text', text }];
    updateBlock(blockIndex, { content: textContent });
  };

  const handleImageUpload = async (blockIndex: number, file: File) => {
    setUploadingBlockIndex(blockIndex);
    setUploadProgress(0);

    try {
      // Check upload permission
      const hasPermission = await SupabaseStorage.checkUploadPermission();
      if (!hasPermission) {
        throw new Error('คุณไม่มีสิทธิ์ในการอัปโหลดรูปภาพ กรุณาเข้าสู่ระบบด้วยบัญชี admin');
      }

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return 80;
          }
          return prev + 20;
        });
      }, 200);

      // Upload to Supabase Storage
      const result = await SupabaseStorage.uploadImage(file, 'blog-content');

      clearInterval(progressInterval);

      if (!result.success) {
        let errorMessage = result.error || 'เกิดข้อผิดพลาดในการอัปโหลด';

        // Provide more helpful error messages
        if (result.error?.includes('bucket')) {
          errorMessage = 'ไม่พบ Storage Bucket กรุณาสร้าง bucket ชื่อ "uploads" ใน Supabase Dashboard';
        } else if (result.error?.includes('policy')) {
          errorMessage = 'ไม่มีสิทธิ์อัปโหลด กรุณาตรวจสอบ RLS Policy ของ Storage';
        }

        throw new Error(errorMessage);
      }

      setUploadProgress(100);

      // Update the block with the uploaded image URL
      updateBlock(blockIndex, {
        attrs: {
          src: result.url!,
          alt: file.name.replace(/\.[^/.]+$/, '') // Remove file extension for alt text
        }
      });

      setTimeout(() => {
        setUploadingBlockIndex(null);
        setUploadProgress(0);
      }, 500);

    } catch (error) {
      console.error('Error uploading image:', error);

      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการอัปโหลด';
      alert(`❌ ${errorMessage}\n\n💡 วิธีแก้ไข:\n1. ตรวจสอบว่าเข้าสู่ระบบด้วยบัญชี admin\n2. สร้าง Storage Bucket ชื่อ "uploads" ใน Supabase\n3. ตั้งค่า RLS Policy ให้อนุญาตการอัปโหลด`);

      setUploadingBlockIndex(null);
      setUploadProgress(0);
    }
  };

  const triggerImageUpload = (blockIndex: number) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleImageUpload(blockIndex, file);
      }
    };
    input.click();
  };

  // Removed automatic Enter key handling - let users type normally

  // Removed heading Enter key handling - let users type normally

  const renderBlockEditor = (block: BlogContent, index: number) => {
    const isSelected = selectedBlockIndex === index;

    return (
      <div
        key={index}
        className={`relative group border-2 rounded-lg p-4 transition-all ${isSelected ? 'border-blue-500 bg-blue-500/5' : 'border-transparent hover:border-white/20'
          }`}
        onClick={() => setSelectedBlockIndex(index)}
      >
        {/* Block Controls */}
        {isSelected && (
          <div className="absolute -top-3 right-2 flex gap-1 bg-black/80 rounded-lg p-1">
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20 min-w-8 h-8"
              onPress={() => moveBlock(index, 'up')}
              disabled={index === 0}
            >
              ↑
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20 min-w-8 h-8"
              onPress={() => moveBlock(index, 'down')}
              disabled={index === content.length - 1}
            >
              ↓
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-red-400 hover:bg-red-500/20 min-w-8 h-8"
              onPress={() => deleteBlock(index)}
            >
              ×
            </Button>
          </div>
        )}

        {/* Block Content */}
        {block.type === 'paragraph' && (
          <div data-block-index={index}>
            <Textarea
              placeholder="เขียนเนื้อหาของคุณที่นี่..."
              value={block.content?.[0]?.text || ''}
              onChange={(e) => updateTextContent(index, e.target.value)}
              classNames={{
                input: "bg-transparent text-white text-base leading-relaxed",
                inputWrapper: "bg-transparent border-none shadow-none"
              }}
              minRows={2}
            />
          </div>
        )}

        {block.type === 'heading' && (
          <div className="space-y-2" data-block-index={index}>
            <div className="flex gap-2 mb-2">
              {[1, 2, 3, 4, 5, 6].map((level) => (
                <Button
                  key={level}
                  size="sm"
                  variant={block.attrs?.level === level ? "solid" : "bordered"}
                  className={
                    block.attrs?.level === level
                      ? "bg-blue-500 text-white"
                      : "border-white/20 text-white hover:bg-white/10"
                  }
                  onPress={() => updateBlock(index, { attrs: { ...block.attrs, level } })}
                >
                  H{level}
                </Button>
              ))}
            </div>
            <Input
              placeholder="หัวข้อ..."
              value={block.content?.[0]?.text || ''}
              onChange={(e) => updateTextContent(index, e.target.value)}
              classNames={{
                input: `bg-transparent text-white font-bold ${block.attrs?.level === 1 ? 'text-3xl' :
                  block.attrs?.level === 2 ? 'text-2xl' :
                    block.attrs?.level === 3 ? 'text-xl' :
                      block.attrs?.level === 4 ? 'text-lg' :
                        'text-base'
                  }`,
                inputWrapper: "bg-transparent border-none shadow-none"
              }}
            />
          </div>
        )}

        {block.type === 'image' && (
          <div className="space-y-3">
            <div className="border-2 border-dashed border-white/20 rounded-lg p-8 text-center">
              {uploadingBlockIndex === index ? (
                <div className="space-y-4">
                  <div className="text-4xl mb-2 text-blue-400">⟳</div>
                  <p className="text-white">กำลังอัปโหลดรูปภาพ...</p>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-gray-400">{uploadProgress}%</p>
                </div>
              ) : block.attrs?.src ? (
                <div className="space-y-2">
                  <img
                    src={block.attrs.src}
                    alt={block.attrs.alt || ''}
                    className="max-w-full h-auto rounded-lg mx-auto"
                  />
                  <Button
                    size="sm"
                    variant="ghost"
                    className="text-blue-400 hover:text-blue-300"
                    onPress={() => triggerImageUpload(index)}
                  >
                    เปลี่ยนรูปภาพ
                  </Button>
                </div>
              ) : (
                <div>
                  <div className="text-4xl mb-2 text-gray-400">▢</div>
                  <Button
                    className="bg-blue-500 text-white hover:bg-blue-600"
                    onPress={() => triggerImageUpload(index)}
                  >
                    เพิ่มรูปภาพ
                  </Button>
                </div>
              )}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Input
                placeholder="URL รูปภาพ"
                value={block.attrs?.src || ''}
                onChange={(e) => updateBlock(index, {
                  attrs: { ...block.attrs, src: e.target.value }
                })}
                classNames={{
                  input: "bg-black/20 text-white",
                  inputWrapper: "bg-black/20 border border-white/20"
                }}
              />
              <Input
                placeholder="คำอธิบายรูปภาพ (Alt text)"
                value={block.attrs?.alt || ''}
                onChange={(e) => updateBlock(index, {
                  attrs: { ...block.attrs, alt: e.target.value }
                })}
                classNames={{
                  input: "bg-black/20 text-white",
                  inputWrapper: "bg-black/20 border border-white/20"
                }}
              />
            </div>
            <Input
              placeholder="คำบรรยายใต้รูป (ไม่บังคับ)"
              value={block.attrs?.caption || ''}
              onChange={(e) => updateBlock(index, {
                attrs: { ...block.attrs, caption: e.target.value }
              })}
              classNames={{
                input: "bg-black/20 text-white",
                inputWrapper: "bg-black/20 border border-white/20"
              }}
            />
          </div>
        )}

        {block.type === 'quote' && (
          <div className="border-l-4 border-blue-500 pl-4">
            <Textarea
              placeholder="ใส่ข้อความที่ต้องการอ้างอิง..."
              value={block.content?.[0]?.text || ''}
              onChange={(e) => updateTextContent(index, e.target.value)}
              classNames={{
                input: "bg-transparent text-white text-lg italic",
                inputWrapper: "bg-transparent border-none shadow-none"
              }}
              minRows={2}
            />
          </div>
        )}

        {block.type === 'code' && (
          <div className="space-y-2">
            <Input
              placeholder="ภาษาโปรแกรม (เช่น javascript, python)"
              value={block.attrs?.language || ''}
              onChange={(e) => updateBlock(index, {
                attrs: { ...block.attrs, language: e.target.value }
              })}
              classNames={{
                input: "bg-black/20 text-white",
                inputWrapper: "bg-black/20 border border-white/20"
              }}
            />
            <Textarea
              placeholder="ใส่โค้ดของคุณที่นี่..."
              value={block.content?.[0]?.text || ''}
              onChange={(e) => updateTextContent(index, e.target.value)}
              classNames={{
                input: "bg-gray-900 text-green-400 font-mono text-sm",
                inputWrapper: "bg-gray-900 border border-gray-700"
              }}
              minRows={4}
            />
          </div>
        )}

        {/* Add Block Button */}
        {isSelected && (
          <div className="flex justify-center mt-4 pt-4 border-t border-white/10">
            <div className="flex gap-2 flex-wrap">
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/10"
                onPress={() => addBlock('paragraph', index)}
              >
                ย่อหน้า
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/10"
                onPress={() => addBlock('heading', index)}
              >
                หัวข้อ
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/10"
                onPress={() => addBlock('image', index)}
              >
                รูปภาพ
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/10"
                onPress={() => addBlock('quote', index)}
              >
                อ้างอิง
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/10"
                onPress={() => addBlock('code', index)}
              >
                โค้ด
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Editor Help */}
      <div className="text-xs text-gray-400 bg-black/20 rounded-lg p-3 border border-white/10">
        <div className="flex flex-wrap gap-4">
          <span><kbd className="bg-gray-700 px-2 py-1 rounded text-xs">Enter</kbd> ขึ้นบรรทัดใหม่</span>
          <span>ใช้ปุ่ม <strong>+ เพิ่มเนื้อหา</strong> เพื่อสร้างบล็อกใหม่</span>
        </div>
      </div>

      {/* Content Blocks */}
      {content.length === 0 ? (
        <Card className="bg-black/20 border border-white/10">
          <CardBody className="p-12 text-center">
            <div className="text-6xl mb-4 text-gray-400">▣</div>
            <h3 className="text-xl font-semibold text-white mb-2">เริ่มเขียนบทความ</h3>
            <p className="text-gray-400 mb-6">เลือกประเภทเนื้อหาที่ต้องการเพิ่ม</p>
            <div className="flex gap-2 justify-center flex-wrap">
              <Button
                className="bg-blue-500 text-white hover:bg-blue-600"
                onPress={() => addBlock('paragraph')}
              >
                ย่อหน้า
              </Button>
              <Button
                className="bg-green-500 text-white hover:bg-green-600"
                onPress={() => addBlock('heading')}
              >
                หัวข้อ
              </Button>
              <Button
                className="bg-purple-500 text-white hover:bg-purple-600"
                onPress={() => addBlock('image')}
              >
                รูปภาพ
              </Button>
            </div>
          </CardBody>
        </Card>
      ) : (
        <>
          {content.map((block, index) => renderBlockEditor(block, index))}

          {/* Add Block at End */}
          <div className="text-center">
            <Button
              variant="bordered"
              className="border-white/20 text-white hover:bg-white/10"
              onPress={() => addBlock('paragraph')}
            >
              + เพิ่มเนื้อหา
            </Button>
          </div>
        </>
      )}
    </div>
  );
}
