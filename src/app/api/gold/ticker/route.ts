import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const requestedSymbol = searchParams.get("symbol") || "PAXGUSDT";

    console.log(`📊 Fetching 24h ticker data for: ${requestedSymbol}`);

    // Try different gold-related symbols if the requested one fails
    const goldSymbols = [
      requestedSymbol,
      "PAXGUSDT", // PAX Gold (1 PAXG = 1 troy ounce)
      "XAUUSDT", // Gold futures (if available)
      "GOLDUSDT", // Alternative gold token
      "BTCUSDT", // BTC as final fallback
    ];

    let response;
    let symbol = requestedSymbol;

    for (const testSymbol of goldSymbols) {
      try {
        console.log(`📊 Trying ticker symbol: ${testSymbol}`);

        response = await fetch(
          `https://api.binance.com/api/v3/ticker/24hr?symbol=${testSymbol}`,
          {
            headers: {
              "User-Agent": "TraderBase/1.0",
            },
          }
        );

        if (response.ok) {
          symbol = testSymbol;
          console.log(`✅ Ticker success with symbol: ${symbol}`);
          break;
        } else {
          console.log(
            `❌ Ticker failed with symbol: ${testSymbol} - ${response.status}`
          );
        }
      } catch (error) {
        console.log(`❌ Ticker error with symbol: ${testSymbol} - ${error}`);
        continue;
      }
    }

    if (!response || !response.ok) {
      throw new Error(
        `All ticker symbols failed. Last status: ${response?.status}`
      );
    }

    const data = await response.json();

    // Debug the ticker response structure
    console.log("📊 Raw ticker response:", JSON.stringify(data, null, 2));
    console.log("📊 Ticker data structure:", {
      symbol: data.symbol,
      closePrice: data.c,
      priceChange: data.p,
      priceChangePercent: data.P,
      highPrice: data.h,
      lowPrice: data.l,
      volume: data.v,
      dataType: typeof data.c,
      hasClosePrice: "c" in data,
    });

    // Try to parse the close price with multiple field names
    let closePrice = "Unknown";
    let priceValue = null;

    // Try different possible field names for current price
    const possiblePriceFields = [
      "c", // Standard Binance field
      "lastPrice", // Alternative field name
      "price", // Generic field name
      "close", // Another possible name
      "last", // Yet another possibility
    ];

    for (const field of possiblePriceFields) {
      if (data[field] !== undefined && data[field] !== null) {
        priceValue = data[field];
        console.log(`✅ Found price in field '${field}':`, priceValue);
        break;
      }
    }

    if (priceValue !== null) {
      try {
        const parsed = parseFloat(priceValue);
        if (!isNaN(parsed) && isFinite(parsed)) {
          closePrice = parsed.toFixed(2);
          console.log(`✅ Successfully parsed price: ${closePrice}`);
        } else {
          console.error("❌ Price value is not a valid number:", priceValue);
        }
      } catch (error) {
        console.error("❌ Error parsing price value:", error);
      }
    } else {
      console.error("❌ No price field found in any of:", possiblePriceFields);
      console.error("❌ Available fields:", Object.keys(data));
    }

    console.log(`💰 Retrieved ticker data: ${data.symbol} - $${closePrice}`);

    // Return the data with CORS headers and symbol info
    return NextResponse.json(
      {
        ...data,
        actualSymbol: symbol,
        message: `Ticker data retrieved for ${symbol}`,
      },
      {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    console.error("❌ Ticker API error:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch ticker data",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      {
        status: 500,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
