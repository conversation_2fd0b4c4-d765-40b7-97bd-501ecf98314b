'use client'

import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { motion } from "framer-motion";
import GoldPriceChart from "./GoldPriceChart";

interface HeroSectionProps {
  livePrice: number;
  priceChange: number;
}

export default function HeroSection({ livePrice, priceChange }: HeroSectionProps) {
  return (
    <div id="hero" className="relative z-10 min-h-screen flex items-center">
      {/* Futuristic Animated Background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated Grid */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/10 to-transparent animate-pulse"></div>
          <div
            className="absolute inset-0 bg-[linear-gradient(90deg,transparent_24px,rgba(59,130,246,0.1)_25px,rgba(59,130,246,0.1)_26px,transparent_27px,transparent_74px,rgba(59,130,246,0.1)_75px,rgba(59,130,246,0.1)_76px,transparent_77px),linear-gradient(rgba(59,130,246,0.1)_24px,transparent_25px,transparent_26px,rgba(59,130,246,0.1)_27px,rgba(59,130,246,0.1)_74px,transparent_75px,transparent_76px,rgba(59,130,246,0.1)_77px)]"
            style={{ backgroundSize: '100px 100px' }}
          ></div>
        </div>

        {/* Floating Geometric Shapes */}
        <motion.div
          className="absolute top-20 left-20 w-4 h-4 border border-blue-400/50 rotate-45"
          animate={{
            y: [0, -20, 0],
            rotate: [45, 135, 45],
            opacity: [0.3, 0.8, 0.3]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute top-40 right-32 w-6 h-6 border border-purple-400/50"
          animate={{
            y: [0, 30, 0],
            rotate: [0, 180, 360],
            opacity: [0.4, 0.9, 0.4]
          }}
          transition={{ duration: 6, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        />
        <motion.div
          className="absolute bottom-32 left-32 w-3 h-3 bg-blue-400/30 rounded-full"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.3, 0.7, 0.3]
          }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 2 }}
        />

        {/* Scanning Lines */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/20 to-transparent w-1"
          animate={{ x: [-100, 1920] }}
          transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
        />
        <motion.div
          className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-400/20 to-transparent h-1"
          animate={{ y: [-100, 1080] }}
          transition={{ duration: 10, repeat: Infinity, ease: "linear", delay: 2 }}
        />

        {/* Holographic Circles */}
        <motion.div
          className="absolute top-1/4 right-1/4 w-32 h-32 border border-blue-400/30 rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 360],
            opacity: [0.2, 0.5, 0.2]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute bottom-1/4 left-1/4 w-24 h-24 border border-purple-400/30 rounded-full"
          animate={{
            scale: [1, 0.8, 1],
            rotate: [360, 0],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{ duration: 6, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        />

        {/* Data Stream Lines */}
        <div className="absolute top-0 left-1/3 w-px h-full bg-gradient-to-b from-transparent via-green-400/40 to-transparent animate-pulse"></div>
        <div className="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-blue-400/40 to-transparent animate-pulse delay-1000"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Main Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <Chip
              className="mb-6 bg-blue-500/20 border border-blue-500/50 text-blue-300"
              variant="bordered"
            >
              การศึกษาเทรด FOREX มืออาชีพ
            </Chip>

            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent leading-tight">
              เรียนรู้
              <br />
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                การเทรด FOREX
              </span>
            </h1>

            <p className="text-xl text-gray-300 leading-relaxed max-w-xl">
              แพลตฟอร์มการเรียนรู้ FOREX ที่สมบูรณ์แบบ ตั้งแต่พื้นฐานสำหรับมือใหม่ไปจนถึงกลยุทธ์มืออาชีพ
              เรียนรู้จากผู้เชี่ยวชาญ เข้าถึงคอร์สฟรี และเริ่มต้นการเดินทางการเทรดด้วยความมั่นใจ
            </p>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                as='a'
                href="/forex-trading-steps"
                className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold px-8 py-3 text-lg"
              >
                เริ่มเรียนฟรี
              </Button>
              <Button
                size="lg"
                as='a'
                href="#brokers"
                variant="bordered"
                className="border-purple-500/50 text-purple-300 hover:bg-purple-500/20 px-8 py-3 text-lg"
              >
                ดูโบรกเกอร์
              </Button>
            </div>

            {/* Live Market Data */}
            {/* <div className="bg-black/40 backdrop-blur-md border border-blue-500/20 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">EUR/USD</p>
                <p className="text-2xl font-bold text-white">{livePrice}</p>
              </div>
              <div className="text-right">
                <p className={`text-sm font-semibold ${priceChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {priceChange >= 0 ? '+' : ''}{priceChange.toFixed(4)}
                </p>
                <p className="text-gray-400 text-xs">Live Price</p>
              </div>
            </div>
          </div> */}
          </motion.div>

          {/* Right Column - Real-time Gold Chart */}
          <GoldPriceChart />
        </div>
      </div>
    </div>
  );
}
