import type { Metadata } from "next";
import { Noto_Sans_Thai } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";

const notoSansThai = Noto_Sans_Thai({
  variable: "--font-noto-sans-thai",
  subsets: ["thai", "latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "TraderBase - เรียนรู้เทรด FOREX ตั้งแต่เริ่มต้น",
  description: "แหล่งเรียนรู้การเทรด FOREX ที่ดีที่สุดในประเทศไทย พร้อมคอร์สฟรี รีวิวโบรกเกอร์ และระบบเทรดมืออาชีพ",
  keywords: "FOREX, เทรด, การเงิน, โบรกเกอร์, ระบบเทรด, การลงทุน, ไทย",
  authors: [{ name: "TraderBase" }],
  creator: "TraderBase",
  publisher: "TraderBase",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://traderbaseth.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "TraderBase - เรียนรู้เทรด FOREX ตั้งแต่เริ่มต้น",
    description: "แหล่งเรียนรู้การเทรด FOREX ที่ดีที่สุดในประเทศไทย พร้อมคอร์สฟรี รีวิวโบรกเกอร์ และระบบเทรดมืออาชีพ",
    url: 'https://traderbaseth.com',
    siteName: 'TraderBase',
    images: [
      {
        url: '/images/seo.png',
        width: 1200,
        height: 630,
        alt: 'TraderBase - เรียนรู้เทรด FOREX ตั้งแต่เริ่มต้น',
      },
    ],
    locale: 'th_TH',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "TraderBase - เรียนรู้เทรด FOREX ตั้งแต่เริ่มต้น",
    description: "แหล่งเรียนรู้การเทรด FOREX ที่ดีที่สุดในประเทศไทย พร้อมคอร์สฟรี รีวิวโบรกเกอร์ และระบบเทรดมืออาชีพ",
    images: ['/images/seo.png'],
    creator: '@TraderBase',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="th" className="dark">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "name": "TraderBase",
              "description": "แหล่งเรียนรู้การเทรด FOREX ที่ดีที่สุดในประเทศไทย",
              "url": "https://traderbaseth.com",
              "image": "https://traderbaseth.com/images/seo.png",
              "sameAs": [
                "https://facebook.com/traderbase",
                "https://twitter.com/traderbase",
                "https://instagram.com/traderbase"
              ],
              "potentialAction": {
                "@type": "SearchAction",
                "target": "https://traderbaseth.com/search?q={search_term_string}",
                "query-input": "required name=search_term_string"
              }
            })
          }}
        />
      </head>
      <body
        className={`${notoSansThai.variable} font-sans antialiased`}
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
