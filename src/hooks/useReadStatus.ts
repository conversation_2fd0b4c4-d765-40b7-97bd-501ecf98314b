"use client";

import { useState, useEffect } from "react";

export function useReadStatus() {
  const [readItems, setReadItems] = useState<Set<string>>(new Set());
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // Set client flag
    setIsClient(true);

    // Load read status from localStorage on mount
    if (typeof window !== "undefined") {
      const savedReadItems = localStorage.getItem("forex-steps-read-items");
      if (savedReadItems) {
        try {
          const parsedItems = JSON.parse(savedReadItems);
          setReadItems(new Set(parsedItems));
        } catch (error) {
          console.error("Error parsing read items from localStorage:", error);
        }
      }
    }
  }, []);

  const markAsRead = (itemId: string) => {
    setReadItems((prev) => {
      const newSet = new Set(prev);
      newSet.add(itemId);

      // Save to localStorage
      if (typeof window !== "undefined") {
        localStorage.setItem(
          "forex-steps-read-items",
          JSON.stringify(Array.from(newSet))
        );
      }

      return newSet;
    });
  };

  const markAsUnread = (itemId: string) => {
    setReadItems((prev) => {
      const newSet = new Set(prev);
      newSet.delete(itemId);

      // Save to localStorage
      if (typeof window !== "undefined") {
        localStorage.setItem(
          "forex-steps-read-items",
          JSON.stringify(Array.from(newSet))
        );
      }

      return newSet;
    });
  };

  const isRead = (itemId: string) => {
    return readItems.has(itemId);
  };

  const toggleReadStatus = (itemId: string) => {
    if (isRead(itemId)) {
      markAsUnread(itemId);
    } else {
      markAsRead(itemId);
    }
  };

  const getReadCount = (prefix: string) => {
    return Array.from(readItems).filter((id) => id.startsWith(prefix)).length;
  };

  const clearAllRead = () => {
    setReadItems(new Set());
    if (typeof window !== "undefined") {
      localStorage.removeItem("forex-steps-read-items");
    }
  };

  return {
    readItems,
    markAsRead,
    markAsUnread,
    isRead,
    toggleReadStatus,
    getReadCount,
    clearAllRead,
    isClient,
  };
}
