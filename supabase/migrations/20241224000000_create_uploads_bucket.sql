-- Migration: Create uploads bucket with RLS policies
-- Created: 2024-12-24
-- Description: Creates a storage bucket for blog images and content uploads with proper security policies

-- Create the uploads bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'uploads',
  'uploads',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Note: RLS is already enabled on storage.objects by default in Supabase

-- Policy 1: Allow public read access to all files in uploads bucket
CREATE POLICY "Public read access for uploads bucket"
ON storage.objects
FOR SELECT
USING (bucket_id = 'uploads');

-- Policy 2: Allow authenticated admin users to upload files
CREATE POLICY "Admin upload access for uploads bucket"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'uploads' 
  AND auth.role() = 'authenticated'
  AND (
    -- Check if user email contains 'admin'
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    -- Check if user has admin role in user_metadata
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    -- Check if user has admin role in app_metadata
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
);

-- Policy 3: Allow admin users to update their own uploaded files
CREATE POLICY "Admin update access for uploads bucket"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'uploads'
  AND auth.role() = 'authenticated'
  AND (
    -- Check if user email contains 'admin'
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    -- Check if user has admin role in user_metadata
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    -- Check if user has admin role in app_metadata
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
  AND owner = auth.uid()
)
WITH CHECK (
  bucket_id = 'uploads'
  AND auth.role() = 'authenticated'
  AND (
    -- Check if user email contains 'admin'
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    -- Check if user has admin role in user_metadata
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    -- Check if user has admin role in app_metadata
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
);

-- Policy 4: Allow admin users to delete their own uploaded files
CREATE POLICY "Admin delete access for uploads bucket"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'uploads'
  AND auth.role() = 'authenticated'
  AND (
    -- Check if user email contains 'admin'
    auth.jwt() ->> 'email' ILIKE '%admin%'
    OR
    -- Check if user has admin role in user_metadata
    (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin'
    OR
    -- Check if user has admin role in app_metadata
    (auth.jwt() -> 'app_metadata' ->> 'role') = 'admin'
  )
  AND owner = auth.uid()
);

-- Note: Indexes and permissions are already set up by Supabase by default
-- No need to create additional indexes or grant permissions manually
