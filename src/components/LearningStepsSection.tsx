'use client'

import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader } from "@heroui/react";
import { motion } from "framer-motion";

export default function LearningStepsSection() {
  const steps = [
    {
      step: "ขั้นตอนที่ 1",
      title: "พื้นฐาน FOREX",
      description: "เรียนรู้ว่า FOREX คืออะไร วิธีการทำงาน และแนวคิดการเทรดพื้นฐาน",
      icon: "1",
      color: "from-blue-500 to-cyan-500",
      topics: ["FOREX คืออะไร?", "คู่สกุลเงิน", "เวลาเปิดตลาด", "คำศัพท์พื้นฐาน"]
    },
    {
      step: "ขั้นตอนที่ 2",
      title: "เลือกโบรกเกอร์",
      description: "เลือกโบรกเกอร์ที่เชื่อถือได้และมีการควบคุมที่เหมาะกับสไตล์การเทรดของคุณ",
      icon: "2",
      color: "from-purple-500 to-blue-500",
      topics: ["รีวิวโบรกเกอร์", "ประเภทบัญชี", "ฝาก/ถอนเงิน", "ตั้งค่าแพลตฟอร์ม"]
    },
    {
      step: "ขั้นตอนที่ 3",
      title: "เครื่องมือเทรด",
      description: "เชี่ยวชาญแพลตฟอร์ม MT4/MT5 และอินดิเคเตอร์การเทรดที่จำเป็น",
      icon: "3",
      color: "from-blue-500 to-purple-500",
      topics: ["ตั้งค่า MT4/MT5", "การวิเคราะห์กราฟ", "อินดิเคเตอร์", "การวิเคราะห์เทคนิค"]
    },
    {
      step: "ขั้นตอนที่ 4",
      title: "การซื้อขาย, วิเคราะห์ Forex",
      description: "พัฒนาและทดสอบกลยุทธ์การเทรดที่ทำกำไรได้ของคุณเอง",
      icon: "4",
      color: "from-purple-500 to-blue-500",
      topics: ["การพัฒนากลยุทธ์", "การจัดการความเสี่ยง", "การทดสอบย้อนหลัง", "การเทรดจริง"]
    }
  ];

  return (
    <div id="learn" className="relative z-10 container mx-auto px-6 py-20">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="text-center mb-16"
      >
        <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
          เส้นทางการเรียนรู้ FOREX ของคุณ
        </h2>
        <p className="text-gray-400 text-lg max-w-2xl mx-auto">
          เชี่ยวชาญการเทรด FOREX ทีละขั้นตอน - จากมือใหม่สู่เทรดเดอร์มืออาชีพ
        </p>
      </motion.div>

      <div className="grid md:grid-cols-4 gap-8 mb-20">
        {steps.map((step, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 + index * 0.1 }}
          >
            <Card className="bg-black/40 backdrop-blur-md border border-blue-500/20 hover:border-blue-500/50 transition-all duration-300 h-full group">
              <CardHeader className="pb-2">
                <div className="flex items-center gap-3 mb-4">
                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${step.color} flex items-center justify-center text-xl font-bold text-white`}>
                    {step.icon}
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">{step.step}</p>
                    <h3 className="text-lg font-semibold text-white">{step.title}</h3>
                  </div>
                </div>
              </CardHeader>
              <CardBody className="pt-0 space-y-4">
                <p className="text-gray-400 text-sm">{step.description}</p>
                <div className="space-y-2">
                  {step.topics.map((topic, topicIndex) => (
                    <div key={topicIndex} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                      <p className="text-gray-300 text-xs">{topic}</p>
                    </div>
                  ))}
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  as='a'
                  href="/forex-trading-steps"
                  className="w-full text-blue-300 border-blue-500/30 hover:bg-blue-500/20 group-hover:border-blue-500/50"
                >
                  เริ่มเรียนรู้
                </Button>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
